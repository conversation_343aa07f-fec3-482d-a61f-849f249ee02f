import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart'; // Add this import

class IconNavigationHelper {
  static List<Map<String, String>> getAllIcons() {
    return [
      {"path": "assets/images/airallowance.svg","label": "Air ticket\nallowance " },
      {"path": "assets/images/applyleaveicon.svg", "label": "Apply leave"},
      {"path": "assets/images/approvalsicon.svg", "label": "Approvals"},
      {"path": "assets/images/claimicon.svg", "label": "Claim"},
      {
        "path": "assets/images/educationicon.svg",
        "label": "Education\nallowance req.",
      },
      {"path": "assets/images/letter-request.svg", "label": "Letter\nrequest"},
      {"path": "assets/images/loanicon.svg", "label": "Loan/advance\nrequest"},
      {"path": "assets/images/nocicon.svg", "label": "N.O.C"},
      {"path": "assets/images/payslipicon.svg", "label": "Pay slip"},
      {"path": "assets/images/peopleicon.svg", "label": "People"},
      {"path": "assets/images/reimbicon.svg", "label": "Reimbursement req."},
      {"path": "assets/images/salary-info.svg", "label": "Salary info\nletter"},
      {"path": "assets/images/toilicon.svg", "label": "TOIL\nrequest"},
      {"path": "assets/images/travelicon.svg", "label": "Travel\nclaims"},
      {"path": "assets/images/wfhicon.svg", "label": "WFH\nrequest"},
    ];
  }

  static void navigateToScreen({
    required BuildContext context,
    required String label,
    required Function(String) onIconClicked,
  }) {
    onIconClicked(label);

    switch (label) {
      case "Reimbursement req.":
        context.push('/reimbursement');
        break;
      case "Approvals":
        context.push('/approvals');
        break;
      case "Education\nallowance req.":
        context.push('/education-allowance');
        break;
      case "Loan/advance\nrequest":
        context.push('/loan-request');
        break;
      case "Claim":
        context.push('/claim');
        break;
      case "Travel\nclaims":
        context.push('/travel-claims');
        break;
      case "TOIL\nrequest":
        context.push('/toil-request');
        break;
      case "Letter\nrequest":
        context.push('/letter-approval');
        break;
      case "WFH\nrequest":
        context.push('/wfh-request');
        break;
      case "Apply leave":
        context.push('/apply-leave');
        break;
      case "Air ticket\nallowance ":
        context.push('/air-ticket');
        break;
      case "People":
        context.push('/people-directory');
        break;
      case "N.O.C":
        context.push('/noc');
        break;
      case "Salary info\nletter":
        context.push('/salary-info');
        break;
      case "Pay slip":
        context.push('/pay-slip');
        break;
    }
  }
}
