import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_profiles')
export class UserProfile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'name', nullable: true, length: 200 })
  name: string;

  @Column({ name: 'user_name', nullable: true, length: 200 })
  UserName: string; //Fusion worker, Fusion Emps

  @Column({ name: 'email', nullable: true, length: 1000 })
  email: string;

  @Column({
    name: 'unique_id',
    nullable: true,
    length: 500,
  })
  UniqueId: string; //Fusion worker, Fusion Emps

  @Column({
    name: 'work_email',
    nullable: true,

    length: 1000,
  })
  WorkEmail: string; //Fusion Emps

  @Column({ name: 'phone', nullable: true, length: 20 })
  phone: string;

  @Column({ name: 'phones', nullable: true, length: 500 })
  phones: string; //Fusion worker

  @Column({ name: 'emails', nullable: true, length: 1000 })
  emails: string; //Fusion worker

  @Column({
    name: 'person_number',
    nullable: true,

    length: 200,
  })
  PersonNumber?: string; //Fusion worker, Fusion Emps

  @Column({ name: 'person_id', nullable: true, length: 500 })
  PersonId: string; //Fusion worker, Fusion Emps

  @Column({
    name: 'last_profile_update_date',
    nullable: true,

    length: 30,
  })
  LastProfileUpdateDate: string; //all

  @Column({
    name: 'last_login_date',
    nullable: true,

    length: 30,
  })
  LastLoginDate: string; //all

  @Column({
    name: 'last_login_by',
    nullable: true,

    length: 200,
  })
  LastLoginBy: string; //all

  @Column({ name: 'type', nullable: true, length: 20 })
  Type: string; //EMS,PMS,NMS,365,

  @Column({
    name: 'worker_type',
    nullable: true,

    length: 200,
  })
  WorkerType: string; //Fusion Emps

  @Column({ name: 'user', nullable: true })
  userId: number; //Fusion Emps, Fusion worker

  @ManyToOne(() => User, (entity) => entity.id, { onDelete: 'NO ACTION' })
  @JoinColumn({ name: 'user' })
  user?: User;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column('bit', {
    name: 'is_active',
    nullable: true,
    default: false,
    transformer: {
      from: (value: Buffer) => (value != null ? Boolean(value[0]) : false),
      to: (value: boolean) => (value ? 1 : 0),
    },
  })
  isActive: boolean;
}
