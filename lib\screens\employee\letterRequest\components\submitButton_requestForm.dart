import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/customSubmitConfirmDialog.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/letterRequest/models/letter_request_submit_model.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_submit_provider.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/screens/employee/letterRequest/providers/link_list_provider.dart';
import 'package:seawork/utils/style/colors.dart';

class SubmitButtonRequestForm extends StatefulWidget {
  final double screenWidth;
  final double screenHeight;
  final bool allRequiredFieldsFilled;
  final List<FileModel> uploadedFiles;
  final dynamic state;
  final WidgetRef ref;
  final BuildContext context;
  final StateProvider<String?> countryProvider;

  // Add all your required controllers and personNumber here
  final String personNumber;
  final TextEditingController accountNumberController;
  final TextEditingController ibanNumberController;
  final TextEditingController bankNameController;
  final TextEditingController branchNameController;
  final TextEditingController swiftCodeController;
  final TextEditingController nameInEnglishController;
  final TextEditingController nameInArabicController;
  final TextEditingController titleInEnglishController;
  final TextEditingController titleInArabicController;
  final TextEditingController mobileNumberController;
  final TextEditingController directNumberController;
  final TextEditingController remarksInEnglishController;
  final TextEditingController remarksInArabicController;
  final TextEditingController travelStartDateController;
  final TextEditingController travelEndDateController;
  final TextEditingController addressedToController;
  // final TextEditingController salaryCertificateDateController;
  final TextEditingController linkController;

  const SubmitButtonRequestForm({
    super.key,
    required this.screenWidth,
    required this.screenHeight,
    required this.allRequiredFieldsFilled,
    required this.uploadedFiles,
    required this.state,
    required this.ref,
    required this.context,
    required this.personNumber,
    required this.accountNumberController,
    required this.ibanNumberController,
    required this.bankNameController,
    required this.branchNameController,
    required this.swiftCodeController,
    required this.nameInEnglishController,
    required this.nameInArabicController,
    required this.titleInEnglishController,
    required this.titleInArabicController,
    required this.mobileNumberController,
    required this.directNumberController,
    required this.remarksInEnglishController,
    required this.remarksInArabicController,
    required this.countryProvider,
    required this.travelStartDateController,
    required this.travelEndDateController,
    // required this.salaryCertificateDateController,
    required this.addressedToController,
    required this.linkController,
  });

  @override
  _SubmitButtonRequestFormState createState() => _SubmitButtonRequestFormState();
}

class _SubmitButtonRequestFormState extends State<SubmitButtonRequestForm> {
  @override
  void initState() {
    super.initState();
    
    // Add listeners to controllers that affect validation
    widget.addressedToController.addListener(_updateButtonState);
    // widget.salaryCertificateDateController.addListener(_updateButtonState);
    widget.travelStartDateController.addListener(_updateButtonState);
    widget.travelEndDateController.addListener(_updateButtonState);
    widget.accountNumberController.addListener(_updateButtonState);
    widget.ibanNumberController.addListener(_updateButtonState);
    widget.bankNameController.addListener(_updateButtonState);
    widget.branchNameController.addListener(_updateButtonState);
    widget.nameInEnglishController.addListener(_updateButtonState);
    widget.nameInArabicController.addListener(_updateButtonState);
    widget.titleInEnglishController.addListener(_updateButtonState);
    widget.titleInArabicController.addListener(_updateButtonState);
    widget.mobileNumberController.addListener(_updateButtonState);
  }

  @override
  void dispose() {
    // Remove listeners to prevent memory leaks
    widget.addressedToController.removeListener(_updateButtonState);
    // widget.salaryCertificateDateController.removeListener(_updateButtonState);
    widget.travelStartDateController.removeListener(_updateButtonState);
    widget.travelEndDateController.removeListener(_updateButtonState);
    widget.accountNumberController.removeListener(_updateButtonState);
    widget.ibanNumberController.removeListener(_updateButtonState);
    widget.bankNameController.removeListener(_updateButtonState);
    widget.branchNameController.removeListener(_updateButtonState);
    widget.nameInEnglishController.removeListener(_updateButtonState);
    widget.nameInArabicController.removeListener(_updateButtonState);
    widget.titleInEnglishController.removeListener(_updateButtonState);
    widget.titleInArabicController.removeListener(_updateButtonState);
    widget.mobileNumberController.removeListener(_updateButtonState);
    super.dispose();
  }

  void _updateButtonState() {
    setState(() {
      // This will trigger a rebuild and recalculate button state
    });
  }

  String convertToSqlDateFormat(String dateStr) {
    try {
      List<String> parts = dateStr.split('/');
      if (parts.length != 3) throw FormatException("Invalid date format");

      String day = parts[0].padLeft(2, '0');
      String month = parts[1].padLeft(2, '0');
      String year = parts[2];

      return '$year-$month-$day'; // YYYY-MM-DD
    } catch (e) {
      throw FormatException('Error converting date: $e');
    }
  }

  // Enhanced validation logic for different document types
  bool _isFormValid(String documentType) {
    switch (documentType) {
      case "Request Bank Change":
        return widget.allRequiredFieldsFilled && widget.uploadedFiles.length > 1;
      
      case "Request Salary Certificate":
        // Both "Addressed to" and "Date" must be filled
        return widget.addressedToController.text.trim().isNotEmpty;
      
      case "Request Embassy Letter":
        // Country, start date, and end date must be filled
        final selectedCountry = widget.ref.read(widget.countryProvider);
        return selectedCountry != null && 
               selectedCountry.isNotEmpty &&
               widget.travelStartDateController.text.trim().isNotEmpty &&
               widget.travelEndDateController.text.trim().isNotEmpty;
      
      case "Request Business Card":
        // All business card fields must be filled (based on your existing logic)
        return widget.allRequiredFieldsFilled;
      
      case "Request NOC for Drivers License":
      case "Request Medical Fitness Letter":
      case "Request Residency Letter":
      case "Request Salary Transfer":
        // These don't have required fields, so always enabled
        return true;
      
      default:
        return widget.allRequiredFieldsFilled;
    }
  }

  Future<void> _handleSubmitRequest() async {
    final documentModel = widget.state.letterRequestType;
    final documentType = documentModel.documentType!;
    final systemDocumentType = documentModel.systemDocumentType!;
    final links = widget.ref.watch(linkListProvider);
    // final flexContext = documentTypeToFlexContext[documentType]!;
 
    final model = LetterRequesSubmitModel(
      documentTypeId: documentModel.documentTypeId!,
      // personNumber: masriPersonNumber,
      documentType: documentType,
      systemDocumentType: systemDocumentType,
      // personId: masriPersonId.toInt()
    );

    List<Map<String, dynamic>> attachments = widget.uploadedFiles.map((file) {
      return {
        "UploadedFileName": file.name,
        "FileContents": base64Encode(file.bytes),
        "UploadedFileContentType": file.mimeType,
        "DatatypeCode": "FILE",
        "FileName": file.name,
        "Title": file.name,
        "ContentRepositoryFileShared": "false",
        "AsyncTrackerId": "3610d472ee014f54b07152853353e958",
      };
    }).toList();

    if (documentType == "Request Bank Change") {
      model.documentRecordsDFF = [
        {
          "__FLEX_Context": systemDocumentType,
          "__FLEX_Context_DisplayValue": documentType,
          "accountNumber": int.parse(widget.accountNumberController.text),
          "ibanNumber": widget.ibanNumberController.text,
          "bankName": widget.bankNameController.text,
          "branchName": widget.branchNameController.text,
          "swiftCode": widget.swiftCodeController.text,
          "links": links
        }
      ];
    } else if (documentType == "Request Business Card") {
      model.documentRecordsDFF = [
        {
          "__FLEX_Context": systemDocumentType,
          "__FLEX_Context_DisplayValue": documentType,
          "nameInEnglish": widget.nameInEnglishController.text,
          "nameInArabic": widget.nameInArabicController.text,
          "titleInEnglish": widget.titleInEnglishController.text,
          "titleInArabic": widget.titleInArabicController.text,
          "mobileNumber": widget.mobileNumberController.text,
          "directNumber": widget.directNumberController.text,
          "remarksInEnglish": widget.remarksInEnglishController.text,
          "remarksInArabic": widget.remarksInArabicController.text,
        }
      ];
      // model.attachments = attachments;
    } else if (documentType == "Request Embassy Letter") {
      final selectedCountry = widget.ref.read(widget.countryProvider);
      String travelStartDate =
          convertToSqlDateFormat(widget.travelStartDateController.text);
      String travelEndDate =
          convertToSqlDateFormat(widget.travelEndDateController.text);
      model.documentRecordsDFF = [
        {
          "__FLEX_Context": systemDocumentType,
          "__FLEX_Context_DisplayValue": documentType,
          "travellingCountry": selectedCountry,
          "travelStartDate": travelStartDate,
          "travelEndDate": travelEndDate,
        }
      ];
    } else if (documentType == "Request Salary Certificate") {
      // String date = convertToSqlDateFormat(widget.salaryCertificateDateController.text);
      model.documentRecordsDFF = [
        {
          "__FLEX_Context": systemDocumentType,
          "__FLEX_Context_DisplayValue": documentType,
          "addressTo": widget.addressedToController.text,
          // "date": date, // Include the date in submission
          "links": links,
        }
      ];
    } else if (documentType == "Request Salary Transfer") {
      model.documentRecordsDFF = [
        {
          "__FLEX_Context": systemDocumentType,
          "__FLEX_Context_DisplayValue": documentType,
        }
      ];
    }
    model.attachments = attachments;
    
    showDialog(
      context: widget.context,
      barrierDismissible: false,
      builder: (_) => const Center(
        child: CustomLoadingWidget(),
      ),
    );
    
    try {
      await widget.ref.read(documentSubmitProvider.notifier).submit(model);
      widget.ref.invalidate(GetDocumentRecordStatusProvider);
      await widget.ref.read(GetDocumentRecordStatusProvider.future);
      await widget.ref.read(letterRequestPendingProvider.notifier).refresh();

      Navigator.of(widget.context).pop();
      showDialog(
        context: widget.context,
        builder:
            (context) => CustomSubmitConfirmDialog(
              content1: 'Document',
              type: 'document',
              content3: documentModel.documentType,
              message:
                  "Your request for letter has been\nsubmitted and ready to be reviewed.",
              onClose: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
                // Navigator.of(context).pop();
                // Navigator.of(context).pop();
              },
              documentType: documentModel.documentType!,
              document: CustomSubmitConfirmDialog,
            ),
      );
    } catch (e) {
      Navigator.of(widget.context).pop(); // Close loading dialog
      
      String errorMessage = "No internet connection. Please check your network and try again.";
      
      if (e is SocketException || 
          e.toString().contains('Failed host lookup') ||
          e.toString().contains('Network is unreachable')) {
        errorMessage = "No internet connection. Please check your network and try again.";
      }
      
      showDialog(
        context: widget.context,
        builder: (context) => AlertDialog(
          title: const Text('Connection Error'),
          content: Text(errorMessage),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _handleSubmitRequest(); // Retry
              },
              child: const Text('Retry'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    }
  }

  String _getValidationMessage(String documentType) {
    switch (documentType) {
      case "Request Salary Certificate":
        if (widget.addressedToController.text.trim().isEmpty) {
          return "Please fill 'Addressed to' field";
        }
        break;
      case "Request Embassy Letter":
        final selectedCountry = widget.ref.read(widget.countryProvider);
        List<String> missing = [];
        if (selectedCountry == null || selectedCountry.isEmpty) missing.add("Country");
        if (widget.travelStartDateController.text.trim().isEmpty) missing.add("Start Date");
        if (widget.travelEndDateController.text.trim().isEmpty) missing.add("End Date");
        if (missing.isNotEmpty) {
          return "Please fill: ${missing.join(', ')}";
        }
        break;
      default:
        return "Please fill all required fields";
    }
    return "Please fill all required fields";
  }

  @override
  Widget build(BuildContext context) {
    final documentType = widget.state.letterRequestType.documentType!;
    final isEnabled = _isFormValid(documentType);

    return Builder(
      builder: (scaffoldContext) {
        return Padding(
          padding: EdgeInsets.all(widget.screenWidth * 0.01),
          child: ElevatedButton(
            onPressed: isEnabled
                ? _handleSubmitRequest
                : () {
                    ScaffoldMessenger.of(scaffoldContext).clearSnackBars();
                    ScaffoldMessenger.of(scaffoldContext).showSnackBar(
                      SnackBar(
                        content: Text(_getValidationMessage(documentType)),
                        backgroundColor: AppColors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
            style: ElevatedButton.styleFrom(
              minimumSize: Size(double.infinity, widget.screenHeight * 0.07),
              backgroundColor:
                  isEnabled ? AppColors.viewColor : AppColors.lightGreyColor2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: DmSansText(
              'Submit request',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color:
                  isEnabled ? AppColors.whiteColor : AppColors.lightGreyshade,
            ),
          ),
        );
      },
    );
  }
}