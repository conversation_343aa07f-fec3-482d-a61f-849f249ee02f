// ignore_for_file: avoid_classes_with_only_static_members

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/utils/language/languageConstants.dart';
import 'package:seawork/utils/util.dart';

final ipAddress = "***********";

// Remove environmentProvider and use a static variable instead
class AppEnvironment {
  static String environment = Environment.dev;
}

// Provide the Dio instance as a Riverpod provider
final dioProvider = Provider<Dio>((ref) {
  return createDio(AppEnvironment.environment);
});

// Provide base URLs as Riverpod providers
final baseUrlEMSProvider = getServiceUrl(ServiceType.ems);
final baseUrlAuthProvider = getServiceUrl(ServiceType.ems);
final baseUrlNMSProvider = getServiceUrl(ServiceType.nms);
final baseUrlPMSProvider = getServiceUrl(ServiceType.pms);

// Helper function to create Dio
Dio createDio( String environment) {
  final dio = Dio();

  dio
    
    ..options.connectTimeout = const Duration(minutes: 2)
    ..options.receiveTimeout = const Duration(minutes: 2)
    ..options.headers = {'Content-Type': 'application/json; charset=utf-8'}
    ..interceptors.add(
      LogInterceptor(
        request: true,
        responseBody: true,
        requestBody: true,
        requestHeader: true,
      ),
    )
    ..interceptors.add(
      InterceptorsWrapper(
        onRequest: (
          RequestOptions options,
          RequestInterceptorHandler handler,
        ) async {
          // getting token
          final token = getStringAsync(PreferencesUtils.SESSION_TOKEN);
          final languageCode = await getCurrentLanguage();
          print("TOKEN $token");
          options.headers.putIfAbsent('Authorization', () => 'Bearer $token');
          options.headers.putIfAbsent('languageCode', () => languageCode);

          return handler.next(options);
        },
        onError: (DioException error, ErrorInterceptorHandler handler) async {
          print('Error status code: ${error.response?.statusCode}');
          if (error.response?.statusCode == 423) {
            //await deleteUser(heading: translation().try_again_heading, message: translation().error_with_your_account_message);
          } else if (error.response?.statusCode == 401 &&
              getStringAsync(PreferencesUtils.SESSION_TOKEN).isNotEmpty) {
            await sessionOut(translation().session_out);
          }
          return handler.next(error);
        },
      ),
    );

  return dio;
}


// Service-specific base URLs using Riverpod ref
String getServiceUrl(ServiceType service) {
  const bool useLocalhost = kIsWeb;
  final environment = AppEnvironment.environment;

  if (useLocalhost) {
    if (kIsWeb) {
      return 'http://localhost:8080';
    } else {
      return 'http://$ipAddress:8080';
    }
  }

  switch (service) {
    case ServiceType.nms:
      return 'https://dev-nurseries-api.sea.ac.ae';
    case ServiceType.ems:
      return 'https://dev-employees-api.sea.ac.ae';
    case ServiceType.pms:
      return 'https://dev-nurseries-api.sea.ac.ae';
  }
}

enum ServiceType { ems, nms, pms }
