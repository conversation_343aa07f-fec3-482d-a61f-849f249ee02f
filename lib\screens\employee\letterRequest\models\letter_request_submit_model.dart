class LetterRequesSubmitModel {
  String? publishDate;
  int? documentsOfRecordId;
  List<Map<String, dynamic>>? attachments;
  List<Map<String, dynamic>>? bannerOverrideMessages;
  String? issuingLocation;
  int? relatedObjectId;
  String? issuedDate;
  String? relatedObjectIdColumn;
  String? relatedObjectName;
  int? assignmentId;
  String? assignmentNumber;
  // List<DocumentRecordsHrDocumentTypesItemPostRequestModel>? hrDocumentTypes;
  String? issuingCountry;
  String? publish;
  String? documentName;
  String? personNumber;
  String? comments;
  // List<DocumentRecordsDocumentRecordsDDFItemPostRequestModel>? documentRecordsDDF;
  List<Map<String, dynamic>>? documentRecordsDDF;
  String documentType;
  // List<DocumentRecordsAttachmentsPreviewItemPostRequestModel>? attachmentsPreview;
  // List<DocumentRecordsDocumentRecordsDFFItemPostRequestModel>? documentRecordsDFF;
  List<Map<String, dynamic>>? documentRecordsDFF;
  String systemDocumentType;
  String? documentNumber;
  String? issuingAuthority;
  int? documentTypeId;
  String? issuingCountryName;
  int? personId;
  String? dateFrom;
  String? country;
  String? documentCode;
  String? dateTo;

  LetterRequesSubmitModel({
    this.publishDate,
    this.documentsOfRecordId,
    this.attachments,
    this.bannerOverrideMessages,
    this.issuingLocation,
    this.relatedObjectId,
    this.issuedDate,
    this.relatedObjectIdColumn,
    this.relatedObjectName,
    this.assignmentId,
    this.assignmentNumber,
    // this.hrDocumentTypes,
    this.issuingCountry,
    this.publish,
    this.documentName,
    this.personNumber,
    this.comments,
    this.documentRecordsDDF,
    required this.documentType,
    // this.attachmentsPreview,
    // this.documentRecordsDFF,
    this.documentRecordsDFF,
    required this.systemDocumentType,
    this.documentNumber,
    this.issuingAuthority,
    this.documentTypeId,
    this.issuingCountryName,
    this.personId,
    this.dateFrom,
    this.country,
    this.documentCode,
    this.dateTo,
  });

  Map<String, dynamic> toJson() => {
        if (publishDate != null) "PublishDate": publishDate,
        if (documentsOfRecordId != null)
          "DocumentsOfRecordId": documentsOfRecordId,
        // if (attachments != null) "attachments": attachments!.map((e) => e.toJson()).toList(),
        if (attachments != null) "attachments": attachments,
        // if (bannerOverrideMessages != null) "bannerOverrideMessages": bannerOverrideMessages!.map((e) => e.toJson()).toList(),
        if (bannerOverrideMessages != null)
          "bannerOverrideMessages": bannerOverrideMessages,
        if (issuingLocation != null) "IssuingLocation": issuingLocation,
        if (relatedObjectId != null) "RelatedObjectId": relatedObjectId,
        if (issuedDate != null) "IssuedDate": issuedDate,
        if (relatedObjectIdColumn != null)
          "RelatedObjectIdColumn": relatedObjectIdColumn,
        if (relatedObjectName != null) "RelatedObjectName": relatedObjectName,
        if (assignmentId != null) "AssignmentId": assignmentId,
        if (assignmentNumber != null) "AssignmentNumber": assignmentNumber,
        // if (hrDocumentTypes != null) "hrDocumentTypes": hrDocumentTypes!.map((e) => e.toJson()).toList(),
        if (issuingCountry != null) "IssuingCountry": issuingCountry,
        if (publish != null) "Publish": publish,
        if (documentName != null) "DocumentName": documentName,
        if (personNumber != null) "PersonNumber": personNumber,
        if (comments != null) "Comments": comments,
        // if (documentRecordsDDF != null) "documentRecordsDDF": documentRecordsDDF!.map((e) => e.toJson()).toList(),
         if (documentRecordsDDF != null)
          "documentRecordsDDF": documentRecordsDDF,
        "DocumentType": documentType,
        // if (attachmentsPreview != null) "attachmentsPreview": attachmentsPreview!.map((e) => e.toJson()).toList(),
        // if (documentRecordsDFF != null) "documentRecordsDFF": documentRecordsDFF!.map((e) => e.toJson()).toList(),
        if (documentRecordsDFF != null)
          "documentRecordsDFF": documentRecordsDFF,
        "SystemDocumentType": systemDocumentType,
        if (documentNumber != null) "DocumentNumber": documentNumber,
        if (issuingAuthority != null) "IssuingAuthority": issuingAuthority,
        if (documentTypeId != null) "DocumentTypeId": documentTypeId,
        if (issuingCountryName != null)
          "IssuingCountryName": issuingCountryName,
        if (personId != null) "PersonId": personId,
        if (dateFrom != null) "DateFrom": dateFrom,
        if (country != null) "Country": country,
        if (documentCode != null) "DocumentCode": documentCode,
        if (dateTo != null) "DateTo": dateTo,
      };

  factory LetterRequesSubmitModel.fromJson(Map<String, dynamic> json) {
    return LetterRequesSubmitModel(
      publishDate: json['PublishDate'],
      documentsOfRecordId: json['DocumentsOfRecordId'],
      // attachments: (json['attachments'] as List<dynamic>?)
      //     ?.map(
      //         (e) => DocumentRecordsAttachmentsItemPostRequestModel.fromJson(e))
      //     .toList(),
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      // bannerOverrideMessages: (json['bannerOverrideMessages'] as List<dynamic>?)
      //     ?.map((e) => DocumentRecordsBannerOverrideMessagesItemPostRequestModel
      //         .fromJson(e))
      //     .toList(),
      bannerOverrideMessages: (json['bannerOverrideMessages'] as List<dynamic>?)
      ?.map((e) => e as Map<String, dynamic>)
      .toList(),
      issuingLocation: json['IssuingLocation'],
      relatedObjectId: json['RelatedObjectId'],
      issuedDate: json['IssuedDate'],
      relatedObjectIdColumn: json['RelatedObjectIdColumn'],
      relatedObjectName: json['RelatedObjectName'],
      assignmentId: json['AssignmentId'],
      assignmentNumber: json['AssignmentNumber'],
      // hrDocumentTypes: (json['hrDocumentTypes'] as List<dynamic>?)
      //     ?.map((e) =>
      //         DocumentRecordsHrDocumentTypesItemPostRequestModel.fromJson(e))
      //     .toList(),
      issuingCountry: json['IssuingCountry'],
      publish: json['Publish'],
      documentName: json['DocumentName'],
      personNumber: json['PersonNumber'],
      comments: json['Comments'],
      // documentRecordsDDF: (json['documentRecordsDDF'] as List<dynamic>?)
      //     ?.map((e) =>
      //         DocumentRecordsDocumentRecordsDDFItemPostRequestModel.fromJson(e))
      //     .toList(),
      documentRecordsDDF: (json['documentRecordsDDF'] as List<dynamic>?)
      ?.map((e) => e as Map<String, dynamic>)
      .toList(),
      documentType: json['DocumentType'],
      // attachmentsPreview: (json['attachmentsPreview'] as List<dynamic>?)
      //     ?.map((e) =>
      //         DocumentRecordsAttachmentsPreviewItemPostRequestModel.fromJson(e))
      //     .toList(),
      // documentRecordsDFF: (json['documentRecordsDFF'] as List<dynamic>?)
      //     ?.map((e) =>
      //         DocumentRecordsDocumentRecordsDFFItemPostRequestModel.fromJson(e))
      //     .toList(),
      documentRecordsDFF: (json['documentRecordsDFF'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      systemDocumentType: json['SystemDocumentType'],
      documentNumber: json['DocumentNumber'],
      issuingAuthority: json['IssuingAuthority'],
      documentTypeId: json['DocumentTypeId'],
      issuingCountryName: json['IssuingCountryName'],
      personId: json['PersonId'],
      dateFrom: json['DateFrom'],
      country: json['Country'],
      documentCode: json['DocumentCode'],
      dateTo: json['DateTo'],
    );
  }
}
