import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:go_router/go_router.dart';
class GetStart extends StatelessWidget {
  const GetStart({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          Positioned.fill(
            child: // Background Image
                SvgPicture.asset(
              'assets/images/loginbackground.svg', // Replace with your SVG image asset
              height: 150,
              width: 150,
              fit: BoxFit.cover,
            ),
          ),

          // Overlay to darken background for better visibility
          Positioned.fill(
            child: Container(
              color: Colors.black.withOpacity(0.4),
            ),
          ),

          // Content Column
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo
                  SvgPicture.asset(
                    'assets/images/logoz.svg', // Replace with your SVG logo asset
                    height: 100,
                    width: 100,
                  ),
                  const SizedBox(height: 40),

                  // Welcome Box
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        DmSansText(
                          "Let's get started",
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),

                        DmSansText(
                          'How would you like to continue?',
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        DmSansText(
                          'Quick setup with your mobile number/email\nor go seamless with UAE Pass',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 50),
                        // Button - Continue with Email/Mobile
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              context.push(
                                '/parent-mobile-verification',
                                extra: {'forRegistration': true},
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors
                                  .transparentColor, // Make background transparent
                              foregroundColor:
                                  AppColors.whiteColor, // Text color
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: const BorderSide(
                                    color: AppColors
                                        .whiteColor), // Add white border
                              ),
                              elevation: 0, // Remove shadow
                            ),
                            child: DmSansText(
                              "Continue with email/mobile number",
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.whiteColor,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),

// OR Divider
                        DmSansText(
                          "or",
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.whiteColor.withOpacity(0.7),
                        ),
                        const SizedBox(height: 8),

// Button - Continue with UAE Pass
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton(
                            onPressed: () {},
                            style: OutlinedButton.styleFrom(
                              side:
                                  const BorderSide(color: AppColors.whiteColor),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: DmSansText(
                              "Continue with UAE pass",
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.whiteColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
