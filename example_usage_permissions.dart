// Example usage of the updated PermissionChecker class
import 'lib/components/permission/permissions.dart';

void main() async {
  // Example 1: Check user permission type
  String userType = await PermissionChecker.permissionChecker();
  print('User type: $userType'); // Output: 'pmsUser', 'emsUser', 'multiUser', or 'unknownUser'
  
  // Example 2: Check specific permission types
  bool isPMS = await PermissionChecker.isPMSUser();
  bool isEMS = await PermissionChecker.isEMSUser();
  bool isMulti = await PermissionChecker.isMultiUser();
  
  print('Is PMS User: $isPMS');
  print('Is EMS User: $isEMS');
  print('Is Multi User: $isMulti');
  
  // Example 3: Get all user types
  List<String> allTypes = await PermissionChecker.getUserTypes();
  print('All user types: $allTypes'); // Output: ['PMS', 'EMS'] or ['PMS'] or ['EMS']
  
  // Example 4: Use in conditional logic
  if (await PermissionChecker.isPMSUser()) {
    print('Show PMS specific features');
  } else if (await PermissionChecker.isEMSUser()) {
    print('Show EMS specific features');
  } else if (await PermissionChecker.isMultiUser()) {
    print('Show both PMS and EMS features');
  } else {
    print('Show login screen or error');
  }
}
