import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class PreferencesUtils {
  // Authentication tokens
  static const String SESSION_TOKEN = 'session_token';
  static const String OFFICE365_ACCESS_TOKEN = 'office365_access_token';
  static const String CODE_VERIFIER = 'code_verifier';
  static const String GRAPH_TOKEN = 'graph_token';

  // User data
  static const String USER = 'user';
  static const String USER_PROFILES = 'user_profiles';
  static const String USER_ID = 'user_id';
  static const String USER_NAME = 'user_name';
  static const String USER_EMAIL = 'user_email';
  static const String EMPLOYEE_DETAILS = 'employee_details';
  static const String PERSON_ID = 'person_id';
static const String PERSON_NUMBER = 'person_number';
static const String GENDER = 'gender';
static const String RELIGION = 'religion';
static const String ROLES = 'roles';


  // New additions
  static const String PARENT_ID = 'parentId';
  static const String SELECTED_ROLE = 'selectedRole';

  // Token storage methods
  static Future<void> setString(String key, String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  static Future<String?> getString(String key) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  // Convenience methods for specific tokens
  static Future<void> setSessionToken(String token) async {
    await setString(SESSION_TOKEN, token);
  }

  static Future<String?> getSessionToken() async {
    return await getString(SESSION_TOKEN);
  }

  static Future<void> setOffice365AccessToken(String token) async {
    await setString(OFFICE365_ACCESS_TOKEN, token);
  }

  static Future<String?> getOffice365AccessToken() async {
    return await getString(OFFICE365_ACCESS_TOKEN);
  }

  static Future<void> setGraphToken(String token) async {
    await setString(GRAPH_TOKEN, token);
  }

  static Future<String?> getGraphToken() async {
    return await getString(GRAPH_TOKEN);
  }

  // User data methods
  static Future<void> setUserData(Map<String, dynamic> user) async {
    await setString(USER, json.encode(user));
  }

  static Future<Map<String, dynamic>?> getUserData() async {
    final userString = await getString(USER);
    return userString != null ? json.decode(userString) : null;
  }

  static Future<void> setUserProfiles(List<dynamic> profiles) async {
    await setString(USER_PROFILES, json.encode(profiles));
  }

  static Future<List<dynamic>?> getUserProfiles() async {
    final profilesString = await getString(USER_PROFILES);
    return profilesString != null ? json.decode(profilesString) : null;
  }

   static Future<void> setEmployeeDetails(String jsonString) async {
    await setString(EMPLOYEE_DETAILS, jsonString);
  }

  // Add method to get employee details JSON string
  static Future<String?> getEmployeeDetails() async {
    return await getString(EMPLOYEE_DETAILS);
  }
  

  static Future<void> setEmployeeSubFields(Map<String, dynamic> json) async {
  try {
    final prefs = await SharedPreferences.getInstance();

    prefs.setString(PERSON_ID, json['PersonId'].toString());
    prefs.setString(PERSON_NUMBER, json['PersonNumber'] ?? '');
    prefs.setString(GENDER, json['Gender'] ?? '');
    prefs.setString(RELIGION, json['Religion'] ?? '');

    final rolesJson = jsonEncode(json['roles'] ?? []);
    prefs.setString(ROLES, rolesJson);
  } catch (e) {
    print('Failed to save employee subfields: $e');
  }
}


  // New methods
  static Future<void> setParentId(dynamic id) async {
    await setString(PARENT_ID, id.toString());
  }

  static Future<String?> getParentId() async {
    return await getString(PARENT_ID);
  }

  static Future<void> setSelectedRole(String role) async {
    await setString(SELECTED_ROLE, role);
  }

  static Future<String?> getSelectedRole() async {
    return await getString(SELECTED_ROLE);
  }

  // Clear methods
  static Future<void> clear() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  static Future<void> clearAuthTokens() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(SESSION_TOKEN);
    await prefs.remove(OFFICE365_ACCESS_TOKEN);
    await prefs.remove(GRAPH_TOKEN);
    await prefs.remove(CODE_VERIFIER);
  }

  static Future<void> remove(String key) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }
}
