import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';


// Define a provider for ProfileRepository
final profileRepositoryProvider = Provider<ProfileRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return ProfileRepository(dio);
});

class ProfileRepository {
  final Dio _dio;

  ProfileRepository(this._dio);
  
  Future<EmployeeDetailModel> getProfileDetails() async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/emps',
        queryParameters: {
          // 'q': 'PersonId=***************',
          'expand': 'all',
        },
      );

      if (response.data is Map<String, dynamic>) {
        return EmployeeDetailModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception('Unexpected response format: ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      throw Exception('Dio error while fetching employee details: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch employee details: $e');
    }
  }
Future<List<Map<String, dynamic>>> getAllEmployees() async {
  List<Map<String, dynamic>> allEmployees = [];
  int batchSize = 100;
  int totalRecordsToFetch = 700; // Safely more than 658
  int totalBatches = (totalRecordsToFetch / batchSize).ceil();

  List<Future<Response>> batchRequests = [];

  for (int i = 0; i < totalBatches; i++) {
    int offset = i * batchSize;
    batchRequests.add(
      _dio.get(
        '${baseUrlEMSProvider}/emps/all',
        queryParameters: {
          'limit': batchSize,
          'offset': offset,
          'expand': 'all',
        },
        options: Options(
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
      ),
    );
  }

  try {
    final responses = await Future.wait(batchRequests);

    for (final response in responses) {
      if (response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        if (data['items'] is List) {
          final List<Map<String, dynamic>> items =
              (data['items'] as List).cast<Map<String, dynamic>>();
          allEmployees.addAll(items);
        }
      }
    }

    // ✅ Sort alphabetically by 'name' (change key if needed)
    allEmployees.sort((a, b) {
      final nameA = (a['name'] ?? '').toString().toLowerCase();
      final nameB = (b['name'] ?? '').toString().toLowerCase();
      return nameA.compareTo(nameB);
    });

    print('🎉 Final total records fetched: ${allEmployees.length}');
    return allEmployees;
  } on DioException catch (e) {
    throw Exception('Network error: ${e.message}');
  }
}

  
  Future<List<dynamic>> getChildAssignments() async {
    try {
      final empsUniqID =
          '00020000000EACED00057708000110D932582A520000004AACED00057372000D6A6176612E73716C2E4461746514FA46683F3566970200007872000E6A6176612E7574696C2E44617465686A81014B597419030000787077080000019679B1880078';
      final response = await _dio.get(
        '${baseUrlEMSProvider}/emps/$empsUniqID/child/assignments',
        queryParameters: {
          'expand': 'all',
          'PersonId': 300000004952723,
        },
      );

      // Handle both array and single object responses
      if (response.data is List) {
        return response.data as List;
      } else if (response.data is Map) {
        if (response.data['items'] is List) {
          return response.data['items'] as List;
        }
        return [response.data];
      } else {
        return [];
      }
    } on DioException catch (e) {
      throw Exception(
          'Dio error while fetching child assignments: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch child assignments: $e');
    }
  }
}

class EmployerService {
  final Dio _dio;
  static const String _empsUniqID = '00020000000EACED00057708000110D931BA52930000004AACED00057372000D6A6176612E73716C2E4461746514FA46683F3566970200007872000E6A6176612E7574696C2E44617465686A81014B597419030000787077080000019679B1880078';
  static const int _personId = 300000004952723;

  EmployerService({Dio? dio}) : _dio = dio ?? Dio();

  Future<List<dynamic>> fetchEmployers() async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/emps/$_empsUniqID/lov/LegalEmployerLOV',
        queryParameters: {
          'expand': 'all',
          'PersonId': _personId,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map && response.data['items'] is List) {
          return response.data['items'] as List<dynamic>;
        } else if (response.data is List) {
          return response.data as List<dynamic>;
        }
        throw Exception('Unexpected response format');
      } else {
        throw Exception('Failed to fetch employers: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Dio error while fetching employers: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error while fetching employers: $e');
    }
  }

  Future<List<dynamic>> fetchRoles() async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/emps/$_empsUniqID/child/roles',
        queryParameters: {
          'expand': 'all',
          'PersonId': _personId,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map && response.data['items'] is List) {
          return response.data['items'] as List<dynamic>;
        } else if (response.data is List) {
          return response.data as List<dynamic>;
        }
        throw Exception('Unexpected response format');
      } else {
        throw Exception('Failed to fetch roles: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Dio error while fetching roles: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error while fetching roles: $e');
    }
  }
}

final employerServiceProvider = Provider<EmployerService>((ref) {
  final dio = ref.watch(dioProvider);
  return EmployerService(dio: dio);
});

final employerProvider = FutureProvider<List<dynamic>>((ref) async {
  final service = ref.read(employerServiceProvider);
  return await service.fetchEmployers();
});

final rolesProvider = FutureProvider<List<dynamic>>((ref) async {
  final service = ref.read(employerServiceProvider);
  return await service.fetchRoles();
});
