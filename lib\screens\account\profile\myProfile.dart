import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/widget/customLanuCard.dart';
import 'package:seawork/components/widget/customPButton.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/account/profile/components/action_card.dart';
import 'package:seawork/screens/account/profile/image.dart';
import 'package:seawork/screens/account/profile/logoutButton.dart';
import 'package:seawork/screens/account/profile/models/userProfileModel.dart';
import 'package:seawork/screens/account/profile/provider/profileProvider.dart';
import 'package:seawork/screens/account/profile/repository/profileRepository.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';

// StateNotifier to manage the selected index
class BottomNavBarNotifier extends StateNotifier<int> {
  BottomNavBarNotifier() : super(3); // Default index

  void updateIndex(int index) {
    state = index;
  }
}

final bottomNavBarProvider = StateNotifierProvider<BottomNavBarNotifier, int>((ref) {
  return BottomNavBarNotifier();
});

class SavedCardsNotifier extends StateNotifier<List<String>> {
  SavedCardsNotifier() : super([]) {
    _loadSavedCards();
  }

  Future<void> _loadSavedCards() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    state = prefs.getStringList('added_cards') ?? [];
  }

  void addCard(String card) async {
    if (!state.contains(card)) {
      state = [...state, card];
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('added_cards', state);
    }
  }
}

final savedCardsProvider = StateNotifierProvider<SavedCardsNotifier, List<String>>((ref) {
  return SavedCardsNotifier();
});

class SelectedCardNotifier extends StateNotifier<String?> {
  SelectedCardNotifier() : super(null);

  void selectCard(String? cardName) {
    state = cardName;
  }

  void clearSelection() {
    state = null;
  }
}

// Replace the simple StateProvider with a StateNotifierProvider for more control
final selectedCardProvider =
    StateNotifierProvider<SelectedCardNotifier, String?>((ref) {
      return SelectedCardNotifier();
    });

final userProfileProvider = FutureProvider<UserProfileModel?>((ref) async {
  final repository = ref.watch(userProfileRepositoryProvider);
  final prefs = await SharedPreferences.getInstance();
  final personId =
      prefs.getString('personId') ?? ''; // Get personId from SharedPreferences
  return await repository.getUserProfileDetails(
    personId,
  ); // Pass personId to the function
});

// final employeeDetailsProvider = FutureProvider<EmployeeDetailModel?>((
//   ref,
// ) async {
//   final repository = ref.watch(profileRepositoryProvider);
//   return await repository.getProfileDetails();
// });


final employeeDetailsProvider = FutureProvider<EmployeeDetailModel?>(
  (ref) async {
    final repository = ref.watch(profileRepositoryProvider);
    final employeeDetails = await repository.getProfileDetails();

    if (employeeDetails != null) {
      final jsonString = jsonEncode(employeeDetails.toJson());
      await PreferencesUtils.setEmployeeDetails(jsonString);
    }

    return employeeDetails;
  },
);


final profileScreenKeyProvider = Provider<UniqueKey>((ref) => UniqueKey());

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with WidgetsBindingObserver, RouteAware {
  final RouteObserver<PageRoute> _routeObserver = RouteObserver<PageRoute>();
  final ScrollController _scrollController = ScrollController();

  void _addCard() {
    print("Add profile button tapped");
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(selectedCardProvider.notifier).clearSelection();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
    ref.read(selectedCardProvider.notifier).clearSelection();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _routeObserver.unsubscribe(this);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didPopNext() {
    ref.read(selectedCardProvider.notifier).clearSelection();
  }

  @override
  void didPushNext() {
    ref.read(selectedCardProvider.notifier).clearSelection();
  }

  @override
  void didPop() {
    ref.read(selectedCardProvider.notifier).clearSelection();
  }

  @override
  Widget build(BuildContext context) {
    final key = ref.watch(profileScreenKeyProvider);
    ref.watch(bottomNavBarProvider);
    ref.watch(savedCardsProvider);
    final selectedCard = ref.watch(selectedCardProvider);
    final userProfileAsync = ref.watch(userProfileProvider);
    final employeeDetailsAsync = ref.watch(employeeDetailsProvider);
print('User Profile: ${userProfileAsync.value}');
    print('Employee Details: ${employeeDetailsAsync}');
    

    return Scaffold(
      // extendBody: true,
      key: key, // Use the key to force rebuild when needed
      backgroundColor: AppColors.secondaryColor,
      body: Column(
        children: [
          Column(
            children: [
            if (employeeDetailsAsync.isLoading || userProfileAsync.isLoading || employeeDetailsAsync.value?.items?.isEmpty == true)
                const Center(child: CircularProgressIndicator(color: AppColors.viewColor,))
              else ...[
                Column(
                  children: [
                    RotatedImageWidget(
                      showEditButton: true,
                      nameData: {
                        "global_name": {
                          "first_name":
                              employeeDetailsAsync.value!.items?.isNotEmpty ==
                                      true
                                  ? employeeDetailsAsync
                                      .value!
                                      .items
                                      ?.first
                                      .firstName ?? "First"
                                  : "First",
                          "middle_name":
                              employeeDetailsAsync.value!.items?.isNotEmpty ==
                                      true
                                  ? employeeDetailsAsync
                                      .value!
                                      .items
                                      ?.first
                                      .lastName ?? "Last"
                                  : "Last",
                        },
                      },
                    ),
                  ],
                ),
                Padding0x10(
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      OpenSansText(
                        '${userProfileAsync.value?.type == 'EMS' ? 'Employee' : userProfileAsync.value?.type ?? 'N/A'}',
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.blackColor,
                        letterSpacing: 0,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).padding.bottom,
                ),
                child: Column(
                  children: [
                    paddingLTRB(
                      20,
                      28,
                      20,
                      20,
                      Row(
                        children: [
                          Expanded(
                            child: AddProfileButton(
                              onTap: _addCard,
                              text: "Add profile",
                              showIcon: true,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding20x0(
                      Container(
                        child: Column(
                          children: [
                            const SizedBox(height: 4),
                            Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadow,
                                    blurRadius: 4.6,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 0),
                               ) ],
                              ),
                              child: ProfileActionCard(
                                title: 'My account',
                                subtitle: 'View profile details',
                                iconPath: 'assets/images/acct_icon.svg',
                                selectedCard: selectedCard,
                                onTap: () async {
                                  ref.read(selectedCardProvider.notifier).selectCard('My account');
                                  ref.read(bottomNavBarProvider.notifier).updateIndex(0);

                                  await context.push('/my-account');

                                  ref.read(selectedCardProvider.notifier).clearSelection();
                                  ref.refresh(profileScreenKeyProvider);
                                },
                              ),
                            ),

                            Container( 
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadow,
                                    blurRadius: 4.6,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                              child: ProfileActionCard(
                                title: 'My documents',
                                subtitle: 'View your document',
                                iconPath: 'assets/images/doc.svg',
                                selectedCard: selectedCard,
                                onTap: () async {
                                  ref.read(selectedCardProvider.notifier).selectCard('My documents');
                                  ref.read(bottomNavBarProvider.notifier).updateIndex(0);
                              
                                  await context.push('/my-documents');
                              
                                  ref.read(selectedCardProvider.notifier).clearSelection();
                                  ref.refresh(profileScreenKeyProvider);
                                },
                              ),
                            ),

                            Container( 
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadow,
                                    blurRadius: 4.6,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                              child: ProfileActionCard(
                                title: 'My assets',
                                subtitle: 'View your assets',
                                iconPath: 'assets/images/assets.svg',
                                selectedCard: selectedCard,
                                onTap: () async {
                                  ref.read(selectedCardProvider.notifier).selectCard('My assets');
                                  ref.read(bottomNavBarProvider.notifier).updateIndex(0);
                              
                                  await context.push('/my-assets');
                              
                                  ref.read(selectedCardProvider.notifier).clearSelection();
                                  ref.refresh(profileScreenKeyProvider);
                                },
                              ),
                            ),

                            Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadow,
                                    blurRadius: 4.6,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                              child: ProfileActionCard(
                                title: 'Privacy settings',
                                subtitle: 'Secure your app',
                                iconPath: 'assets/images/privacy.svg',
                                selectedCard: selectedCard,
                                onTap: () async {
                                  ref.read(selectedCardProvider.notifier).selectCard('Privacy settings');
                                  await context.push('/privacy-settings');
                                  ref.read(selectedCardProvider.notifier).clearSelection();
                                  ref.refresh(profileScreenKeyProvider);
                                },
                              ),
                            ),

                            const SizedBox(height: 4),
                            Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadow,
                                    blurRadius: 4.6,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 0),
                               ) ],
                              ),
                              child: LanguageCard()),

                            const SizedBox(height: 4),
                            Container(
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadow,
                                    blurRadius: 4.6,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                              child: ProfileActionCard(
                                title: 'Support',
                                subtitle: 'Get help and support',
                                iconPath: 'assets/images/support.svg',
                                selectedCard: selectedCard,
                                onTap: () async {
                                  ref.read(selectedCardProvider.notifier).selectCard('Support');
                                  ref.read(selectedCardProvider.notifier).clearSelection();
                                  ref.refresh(profileScreenKeyProvider);
                                },
                              ),
                            ),
                            const SizedBox(height: 56),
                          ],
                        ),
                      ),
                    ),
                    Padding10x0(LogoutButton()),
                    SizedBox(height: 48),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavigationBar(
        onTap: (index) {
          ref.read(selectedCardProvider.notifier).clearSelection();
          ref.read(bottomNavBarProvider.notifier).updateIndex(index);
          ref.refresh(profileScreenKeyProvider);
        },
      ),
    );
  }
}