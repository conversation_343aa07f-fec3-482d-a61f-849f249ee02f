import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pinput/pinput.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/public/login/counter.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/public/login/loginProvider/loginProvider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/public/registration/provider/registrationProvider.dart';
import 'package:seawork/constants/constantTexts.dart';
import 'dart:convert';
import 'package:go_router/go_router.dart';

class OtpScreen extends ConsumerStatefulWidget {
  final String phoneNumber;
  final String otp;
  final bool forRegistrationPhone;
  final String? requestId;
  final String? email;
  final bool forRegistrationEmail;

  const OtpScreen({
    super.key,
    this.phoneNumber = '',
    this.otp = '',
    this.forRegistrationPhone = false,
    this.requestId,
    this.email,
    this.forRegistrationEmail = false,
  });

  @override
  ConsumerState<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends ConsumerState<OtpScreen> {
  final pinController = TextEditingController();
  bool isOtpIncorrect = false;
  bool isSubmitLoading = false;
  bool isResendLoading = false;
  bool canResend = false;
  bool shouldRestartTimer = false;

  @override
  void initState() {
    super.initState();
    pinController.addListener(_validateOtp);
  }

  void _validateOtp() {
    setState(() {
      // For login scenarios, validate against widget.otp
      // For registration scenarios, don't validate here as it's handled by API
      if (widget.forRegistrationPhone || widget.forRegistrationEmail) {
        // For registration, reset isOtpIncorrect when user types
        // The actual validation will be done by the API call
        isOtpIncorrect = false;
      } else {
        // For login, validate against the provided OTP
        isOtpIncorrect =
            pinController.text.length == 5 && pinController.text != widget.otp;
      }
    });
  }

  Color get _buttonColor {
    return pinController.text.length == 5 && !isSubmitLoading
        ? AppColors.whiteColor
        : AppColors.lightGreyColor2;
  }

  bool get _isSubmitEnabled {
    return pinController.text.length == 5 && !isSubmitLoading;
  }

  void _onTimerComplete() {
    setState(() {
      canResend = true;
    });
  }

  Future<void> _resendOtp() async {
    if (!canResend || isResendLoading) return;

    setState(() {
      isResendLoading = true;
      canResend = false;
      shouldRestartTimer = true;
    });

    try {
      if (widget.forRegistrationPhone) {
        // For registration phone OTP
        await ref
            .read(sendSignUpOtpProvider.notifier)
            .sendOtp(widget.phoneNumber);
        final state = ref.read(sendSignUpOtpProvider);
        state.when(
          data: (data) {
            final error = data['error']?.toString() ?? '';
            if (error.isEmpty) {
              Fluttertoast.showToast(msg: ConstantTexts.otpResentSuccessfully);
              setState(() {
                pinController.clear();
                isOtpIncorrect = false;
              });
            } else {
              Fluttertoast.showToast(msg: error);
            }
          },
          loading: () {},
          error: (e, _) =>
              Fluttertoast.showToast(msg: '${ConstantTexts.errorPrefix}$e'),
        );
      } else if (widget.forRegistrationEmail) {
        // For registration email OTP
        await ref
            .read(sendSignUpEmailOtpProvider.notifier)
            .sendOtp(widget.email!);
        final state = ref.read(sendSignUpEmailOtpProvider);
        state.when(
          data: (data) {
            final error = data['error']?.toString() ?? '';
            if (error.isEmpty) {
              Fluttertoast.showToast(msg: ConstantTexts.otpResentSuccessfully);
              setState(() {
                pinController.clear();
                isOtpIncorrect = false;
              });
            } else {
              Fluttertoast.showToast(msg: error);
            }
          },
          loading: () {},
          error: (e, _) =>
              Fluttertoast.showToast(msg: '${ConstantTexts.errorPrefix}$e'),
        );
      } else {
        // For login phone OTP
        await ref
            .read(generateOtpProvider.notifier)
            .generateOtp(widget.phoneNumber);
        final state = ref.read(generateOtpProvider);
        state.when(
          data: (data) {
            final otp = data['randomNo']?.toString() ?? '';
            if (otp.isNotEmpty) {
              Fluttertoast.showToast(msg: ConstantTexts.otpResentSuccessfully);
              setState(() {
                pinController.clear();
                isOtpIncorrect = false;
              });
            } else {
              Fluttertoast.showToast(msg: ConstantTexts.failedToResendOtp);
            }
          },
          loading: () {},
          error: (e, _) =>
              Fluttertoast.showToast(msg: '${ConstantTexts.errorPrefix}$e'),
        );
      }
    } finally {
      setState(() {
        isResendLoading = false;
        shouldRestartTimer = false;
      });
    }
  }

  Future<void> _handleSubmit() async {
    final enteredOtp = pinController.text.trim();
    if (enteredOtp.isEmpty || enteredOtp.length != 5) {
      Fluttertoast.showToast(
        msg: ConstantTexts.pleaseEnterFiveDigitOtp,
      );
      return;
    }

    if (widget.forRegistrationPhone) {
      // Registration with phone only
      if (widget.requestId == null) {
        Fluttertoast.showToast(
          msg: ConstantTexts.requestIdMissingForRegistration,
        );
        return;
      }
      await ref.read(verifySignUpOtpProvider.notifier).verifyOtp(
            widget.phoneNumber,
            widget.requestId!,
            enteredOtp,
          );
      final state = ref.read(verifySignUpOtpProvider);
      state.when(
        data: (data) async {
          final error = data['error']?.toString() ?? '';
          final result = data['result']?.toString() ?? '';
          if (error == ConstantTexts.invalidOtpLogin) {
            setState(() {
              isOtpIncorrect = true;
            });
            Fluttertoast.showToast(
              msg: ConstantTexts.invalidOtpTryAgain,
            );
            return;
          } else if (error.isEmpty &&
              result == ConstantTexts.otpSuccessfullyLogin) {
            setState(() {
              isOtpIncorrect = false;
            });
            // Now call signUpVerifyAll
            await ref.read(signUpVerifyAllProvider.notifier).signUpVerifyAll(
                  mobileNo: widget.phoneNumber,
                  mobileOtp: enteredOtp,
                  requestId: widget.requestId!,
                  isSignupWithoutEmail: true,
                );
            final signUpState = ref.read(
              signUpVerifyAllProvider,
            );
            signUpState.when(
              data: (signUpData) async {
                final token = signUpData['token'];
                final user = signUpData['user'];
                final userProfiles = signUpData['userProfiles'];
                final parentId = signUpData['parentId'];
                if (token != null &&
                    user != null &&
                    userProfiles != null &&
                    parentId != null) {
                  await PreferencesUtils.setString(
                    PreferencesUtils.SESSION_TOKEN,
                    token,
                  );
                  await PreferencesUtils.setString(
                    PreferencesUtils.USER,
                    json.encode(user),
                  );
                  await PreferencesUtils.setString(
                    PreferencesUtils.USER_PROFILES,
                    json.encode(userProfiles),
                  );
                  await PreferencesUtils.setParentId(
                    parentId,
                  );
                  print('Parent ID saved in otp registrationphone: $parentId');
                  print('User profiles Saved in otp registrationphone: $userProfiles');
                  print('User Saved in otp registrationphone: $user');
                  if (context.mounted) {
                    context.go('/role-selection');
                  }
                } else {
                  Fluttertoast.showToast(
                    msg: ConstantTexts.registrationFailedMissingData,
                  );
                }
              },
              loading: () => Fluttertoast.showToast(
                msg: ConstantTexts.completingRegistration,
              ),
              error: (e, _) => Fluttertoast.showToast(
                msg: '${ConstantTexts.errorPrefix}$e',
              ),
            );
          } else {
            Fluttertoast.showToast(
              msg: error.isNotEmpty
                  ? error
                  : ConstantTexts.otpVerificationFailed,
            );
          }
        },
        loading: () => Fluttertoast.showToast(
          msg: ConstantTexts.validatingOtp,
        ),
        error: (e, _) {
          setState(() {
            isOtpIncorrect = true;
          });
          Fluttertoast.showToast(msg: '${ConstantTexts.errorPrefix}$e');
        },
      );
    } else if (widget.forRegistrationEmail) {
      // Registration with email
      if (widget.email == null) {
        Fluttertoast.showToast(
          msg: 'Email missing for registration',
        );
        return;
      }
      await ref
          .read(verifySignUpEmailOtpProvider.notifier)
          .verifyOtp(widget.email!, enteredOtp);
      final state = ref.read(
        verifySignUpEmailOtpProvider,
      );
      state.when(
        data: (data) async {
          final error = data['error']?.toString() ?? '';
          final result = data['result']?.toString() ?? '';
          if (error.isNotEmpty) {
            setState(() {
              isOtpIncorrect = true;
            });
            Fluttertoast.showToast(msg: error);
            return;
          } else if (result == ConstantTexts.otpSuccessfullyLogin) {
            setState(() {
              isOtpIncorrect = false;
            });
            // Now call signUpVerifyAll
            await ref.read(signUpVerifyAllProvider.notifier).signUpVerifyAll(
                  mobileNo:
                      '', // You may need to pass the phone number if available
                  mobileOtp:
                      '', // You may need to pass the phone OTP if available
                  requestId:
                      '', // You may need to pass the requestId if available
                  email: widget.email!,
                  emailOtp: enteredOtp,
                  isSignupWithoutEmail: false,
                );
            final signUpState = ref.read(
              signUpVerifyAllProvider,
            );
            signUpState.when(
              data: (signUpData) async {
                final token = signUpData['token'];
                final user = signUpData['user'];
                final userProfiles = signUpData['userProfiles'];
                final parentId = signUpData['parentId'];
                if (token != null &&
                    user != null &&
                    userProfiles != null &&
                    parentId != null) {
                  await PreferencesUtils.setString(
                    PreferencesUtils.SESSION_TOKEN,
                    token,
                  );
                  await PreferencesUtils.setString(
                    PreferencesUtils.USER,
                    json.encode(user),
                  );
                  await PreferencesUtils.setString(
                    PreferencesUtils.USER_PROFILES,
                    json.encode(userProfiles),
                  );
                  await PreferencesUtils.setParentId(
                    parentId,
                  );
                  print('Parent ID saved in otp registrationemail: $parentId');
                  print('User profiles Saved in otp registrationemail: $userProfiles');
                  print('User Saved in otp registrationemail: $user');
                  if (context.mounted) {
                    context.go('/role-selection');
                  }
                } else {
                  Fluttertoast.showToast(
                    msg: ConstantTexts.registrationFailedMissingData,
                  );
                }
              },
              loading: () => Fluttertoast.showToast(
                msg: ConstantTexts.completingRegistration,
              ),
              error: (e, _) => Fluttertoast.showToast(
                msg: '${ConstantTexts.errorPrefix}$e',
              ),
            );
          } else {
            Fluttertoast.showToast(
              msg: ConstantTexts.otpVerificationFailed,
            );
          }
        },
        loading: () => Fluttertoast.showToast(
          msg: ConstantTexts.validatingOtp,
        ),
        error: (e, _) {
          setState(() {
            isOtpIncorrect = true;
          });
          Fluttertoast.showToast(msg: '${ConstantTexts.errorPrefix}$e');
        },
      );
    } else {
      if (enteredOtp.length != 5) {
        Fluttertoast.showToast(
          msg: ConstantTexts.pleaseEnterCompleteFiveDigitOtp,
        );
        return;
      }
      // Call the provider to validate OTP
      await ref
          .read(validateOtpProvider.notifier)
          .validateOtp(widget.phoneNumber, enteredOtp);
      final state = ref.read(validateOtpProvider);
      state.when(
        data: (data) {
          final token = data['token'];
          final user = data['user'];
          final userProfiles = data['userProfiles'];
          if (token != null && user != null && userProfiles != null) {
            handleSessionToken(token, user, userProfiles);
            print('Session Token: $token');
            print('User after login: $user');
            print('User Profiles after login: $userProfiles');
          } else {
            Fluttertoast.showToast(
              msg: ConstantTexts.failedToValidateOtp,
            );
          }
        },
        loading: () => Fluttertoast.showToast(
          msg: ConstantTexts.validatingOtp,
        ),
        error: (e, _) =>
            Fluttertoast.showToast(msg: '${ConstantTexts.errorPrefix}$e'),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: SvgPicture.asset(
              'assets/images/loginbackground.svg',
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/logoz.svg',
                      height: 100,
                      width: 100,
                    ),
                    const SizedBox(height: 24),
                    DmSansText(
                      ConstantTexts.otpVerificationTitle,
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    DmSansText(
                      'Please enter the code we sent to\n+971 ${widget.phoneNumber}',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 30),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          DmSansText(
                            ConstantTexts.enterOtpLabel,
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Colors.white,
                          ),
                          const SizedBox(height: 8),
                          Pinput(
                            length: 5,
                            controller: pinController,
                            defaultPinTheme: PinTheme(
                              width: 56,
                              height: 56,
                              decoration: BoxDecoration(
                                color: isOtpIncorrect
                                    ? Colors.red.shade100
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              textStyle: TextStyle(
                                color:
                                    isOtpIncorrect ? Colors.red : Colors.black,
                              ),
                            ),
                          ),
                          if (isOtpIncorrect)
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: OpenSansText(
                                    ConstantTexts.invalidOtpTryAgain,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 0,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        OtpTimer(
                          onTimerComplete: _onTimerComplete,
                          shouldRestart: shouldRestartTimer,
                        )
                      ],
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        // enable only when 5 digits & not loading
                        onPressed: (_isSubmitEnabled)
                            ? () async {
                                setState(() => isSubmitLoading = true);
                                try {
                                  await _handleSubmit();
                                } finally {
                                  if (mounted) {
                                    setState(() => isSubmitLoading = false);
                                  }
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _buttonColor,
                          foregroundColor: AppColors.blackColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: isSubmitLoading
                            ? const SizedBox(
                                width: 18,
                                height: 18,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              )
                            : DmSansText(
                                'Submit',
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: (_buttonColor == AppColors.whiteColor)
                                    ? Colors.black
                                    : Colors.white,
                              ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    TextButton(
                      // Only active after timer ends and not loading
                      onPressed: (canResend && !isResendLoading)
                          ? () async => await _resendOtp()
                          : null,
                      child: isResendLoading
                          ? const SizedBox(
                              width: 18,
                              height: 18,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : DmSansText(
                              canResend
                                  ? ConstantTexts.resendCodeText
                                  : '',
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                              underline: true,
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> handleSessionToken(
    String token,
    Map<String, dynamic> user,
    List<dynamic> userProfiles,
  ) async {
    
    print('Handling session token atttttttt');
    await PreferencesUtils.setString(PreferencesUtils.SESSION_TOKEN, token);
    await PreferencesUtils.setString(PreferencesUtils.USER, json.encode(user));
    await PreferencesUtils.setString(
      PreferencesUtils.USER_PROFILES,
      json.encode(userProfiles),
    );
    print('Session token saved after otp login verification: $token');
    print('User saved after otp login verification: $user');
    print('User profiles saved after otp login verification: $userProfiles');
    final parentId = userProfiles.isNotEmpty ? userProfiles.first['PersonId'] ?? '' : '';
    await PreferencesUtils.setString(PreferencesUtils.PARENT_ID, parentId);

    if (userProfiles.isNotEmpty && userProfiles.first['Type'] == 'PMS') {
      // Use GoRouter for navigation to profile dashboard
      if (context.mounted) {
        context.go('/profile-dashboard');
      }
    } else {
      // Use GoRouter for navigation to main dashboard
      if (context.mounted) {
        context.go('/dashboard');
      }
    }
    // Navigate to Dashboard
  }

  @override
  void dispose() {
    pinController.dispose();
    super.dispose();
  }
}
