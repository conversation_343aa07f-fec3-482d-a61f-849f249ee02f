
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/employee/approval/models/taskCountsModel.dart';

import 'package:seawork/screens/employee/approval/models/taskdetailhistorymodel.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailsmodel.dart';

class BpmApiClient {
  
  final Dio _dio;
  BpmApiClient(this._dio);
  Future<TaskListResponse> getTasks(
      {String? status,
      int offset = 0,
      int limit = 25,
      DateTime? startDate,
      DateTime? endDate,
      String? searchQuery}) async {
    try {
      Map<String, dynamic> queryParams = {
        'limit': limit,
        'offset': offset,
        'assignment': 'ALL',
        'forApproval': true
      };

      if (status != null) {
        queryParams['status'] = status;
      }
      if (startDate != null) {
        queryParams['startDate'] = DateFormat('yyyy-MM-dd').format(startDate);
      }
      if (endDate != null) {
        queryParams['endDate'] = DateFormat('yyyy-MM-dd').format(endDate);
      }
      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParams['search'] = searchQuery;
      }
      print('Fetching tasks with params: $queryParams');
      final response = await _dio.get(
        '${baseUrlEMSProvider}/tasks',
        queryParameters: queryParams,
      );

      // Validate response before parsing
      if (response.data == null) {
        throw Exception('Null response data received');
      }

      // Create a safe copy of the response data to handle null values
      final Map<String, dynamic> safeData =
          Map<String, dynamic>.from(response.data);

      // Ensure items is an array
      if (safeData['items'] == null) {
        safeData['items'] = [];
      }

      // Process each item to avoid null string errors
      final List<dynamic> items = List.from(safeData['items']);
      for (int i = 0; i < items.length; i++) {
        Map<String, dynamic> item = Map<String, dynamic>.from(items[i]);

        // Replace nulls with empty strings for string fields
        item['title'] = item['title'] ?? '';
        item['category'] = item['category'] ?? '';
        item['createdBy'] = item['createdBy'] ?? '';
        item['createdDate'] = item['createdDate'] ?? '';
        item['state'] = item['state'] ?? '';
        item['taskDefinitionName'] = item['taskDefinitionName'] ?? '';

        // Ensure assignees structure is valid
        if (item['assignees'] == null) {
          item['assignees'] = {'items': []};
        } else if (item['assignees']['items'] == null) {
          item['assignees']['items'] = [];
        }

        items[i] = item;
      }

      safeData['items'] = items;
      safeData['count'] = safeData['count'] ?? 0;
      safeData['hasMore'] = safeData['hasMore'] ?? false;

      return TaskListResponse.fromJson(safeData);
    } catch (e) {
      print('Error in getTasks: $e');
      // Return empty response on error instead of throwing
      return TaskListResponse(
        count: 0,
        hasMore: false,
        items: [],
      );
    }
  }

  Future<TaskCountsResponse> getTaskCounts() async {
    try {
      print('Fetching task counts');
      final response = await _dio.get('${baseUrlEMSProvider}/tasksCount');
      return TaskCountsResponse.fromJson(response.data);
    } catch (e) {
      print('Error in getTaskCounts: $e');
      rethrow;
    }
  }

  Future<TaskDetailsModel> getTaskDetails(String identificationKey) async {
    try {
      print('Fetching task details for: $identificationKey');
      final response = await _dio
          .get('${baseUrlEMSProvider}/tasks/$identificationKey');
      return TaskDetailsModel.fromJson(response.data);
    } catch (e) {
      print('Error in getTaskDetails: $e');
      rethrow;
    }
  }

  Future<TaskHistoryResponse> getTaskHistory(String taskId) async {
    try {
      print('Fetching task history for: $taskId');
      final response =
          await _dio.get('${baseUrlEMSProvider}/tasks/$taskId/history');
      return TaskHistoryResponse.fromJson(response.data);
    } catch (e) {
      print('Error in getTaskHistory: $e');
      rethrow;
    }
  }

  Future<bool> approveOrRejectTask({
    required String taskId,
    required bool isApprove,
    required String remarks,
  }) async {
    final Map<String, dynamic> requestBody = {
      "tasks": [taskId],
      "action": {"id": isApprove ? "APPROVE" : "REJECT"},
      "comment": {"commentStr": remarks, "commentScope": "TASK"},
    };

    try {
      print('Approving/Rejecting task: $taskId');
      final response = await _dio.put(
        '${baseUrlEMSProvider}/tasks',
        data: requestBody,
      );

      // Simply check if the response was successful (HTTP 200)
      return response.statusCode == 200;
    } catch (e) {
      print('Error in approveOrRejectTask: $e');
      return false;
    }
  }
}
