import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
// import 'package:seawork/global/network_module.dart';

import 'package:http_parser/http_parser.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/data/module/networkModule.dart';

import 'package:seawork/screens/employee/absence/models/absenceAttachmentResponseModel.dart';
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
import 'package:seawork/screens/employee/absence/models/planBalanceSummaryResponse.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';

import 'package:seawork/screens/employee/task/models/taskModel.dart';

import '../../approval/models/taskdetailsmodel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';

class LeaveTypesRepository extends ChangeNotifier {
    final Dio _dio;

  LeaveTypesRepository(this._dio);
  // final Dio _dio;
  List<AbsenceItem> _fullAbsencesList =
      []; // Global variable to store full absence list
  // Store categorized lists globally
  List<AbsenceItem> _awaitingList = [];
  List<AbsenceItem> _approvedList = [];
  List<AbsenceItem> _deniedList = [];
  List<AbsenceItem> _withdrawnList = []; // New list for cancelled absences>
  List<AbsenceItem> _draftList = [];
  List<String> attachmentIds = [];
  // LeaveTypesRepository() : _dio = Dio();

  void updateStatusCount(String status, int count) {
    _totalStatusCounts[status] = count;
    notifyListeners(); // Notifies UI to rebuild
  }

  // Optional: a method to force notify if internal lists change
  void notify() {
    notifyListeners();
  }

  final Map<String, int> _totalStatusCounts = {
    "AWAITING": 0,
    "APPROVED": 0,
    "DENIED": 0,
    "ORA_WITHDRAWN": 0,
    "SAVED": 0,
  };

  Map<String, int> get totalStatusCounts => _totalStatusCounts;


  Future<List<Map<String, dynamic>>> getLeaveTypes() async {
    try {
      // final personId = 300000015297106;
      final response = await _dio.get(
        '${baseUrlEMSProvider}/absenceTypesLOV',
        queryParameters: {'finder': 'findByWord;PersonId=$masriPersonId'},
      );

      // Extract 'items' from response data
      final List<dynamic> itemsJson = response.data['items'] ?? [];

      // Convert each item to a map with name & id
      List<Map<String, dynamic>> leaveItems =
          itemsJson.map((json) {
            return {
              'id': json['AbsenceTypeId'],
              'name': json['AbsenceTypeName'],
            };
          }).toList();
      return leaveItems;
    } catch (e) {
      throw Exception('Failed to fetch Leave types');
    }
  }

  Future<Map<String, List<AbsenceItem>>> getAbsences({
    required int? offset,
    required String absenceTypeId,
    String? dateFilter,
    String? approvalStatusCd,
    String? absenceStatusCd,
  }) async {
    try {
      String query = '';
      if (dateFilter != null && dateFilter.isNotEmpty) {
        query += '$dateFilter';
      }
      if (absenceTypeId.isNotEmpty) query += 'absenceTypeId=$absenceTypeId;';
      if (approvalStatusCd != null) {
        query += 'approvalStatusCd=$approvalStatusCd;';
      }
      if (absenceStatusCd != null) {
        query += 'absenceStatusCd=$absenceStatusCd;';
      }
      final absenceAttachments = 'absenceAttachments';
      final response = await _dio.get(
        '${baseUrlEMSProvider}/absences?expand=$absenceAttachments&q=$query&limit=10&offset=$offset&orderBy=creationDate:desc',
      );

      final jsonResponse = response.data;
      final GetAbsences getAbsences = GetAbsences.fromJson(jsonResponse);
      // Save totalResults count based on current approvalStatusCd
      final statusKey = approvalStatusCd ?? absenceStatusCd;

      if ((statusKey != null) && jsonResponse['totalResults'] != null) {
        _totalStatusCounts[statusKey.toUpperCase()] =
            jsonResponse['totalResults'];
      }
      attachmentIds.clear();
      _fullAbsencesList = getAbsences.items;

      _awaitingList.clear();
      _approvedList.clear();
      _deniedList.clear();
      _draftList.clear();
      _withdrawnList.clear();

      Map<String, List<AbsenceItem>> groupedAbsences = {
        "AWAITING": [],
        "APPROVED": [],
        "DENIED": [],
        "WITHDRAWN": [],
        "SAVED": [],
      };

      for (var item in getAbsences.items) {
        final status =
            (item.approvalStatusCd == "APPROVED" &&
                    item.absenceStatusCd == "ORA_WITHDRAWN")
                ? "WITHDRAWN"
                : item.approvalStatusCd ?? item.absenceStatusCd ?? "UNKNOWN";

        if (status == "AWAITING") {
          _awaitingList.add(item);
          groupedAbsences["AWAITING"]?.add(item);
        } else if (status == "APPROVED") {
          _approvedList.add(item);
          groupedAbsences["APPROVED"]?.add(item);
        } else if (status == "DENIED") {
          _deniedList.add(item);
          groupedAbsences["DENIED"]?.add(item);
        } else if (status == "WITHDRAWN") {
          _withdrawnList.add(item);
          groupedAbsences["WITHDRAWN"]?.add(item);
        } else if (status == "SAVED") {
          _draftList.add(item);
          groupedAbsences["SAVED"]?.add(item);
        }
      }

      return groupedAbsences;
    } catch (e) {
      throw Exception('Failed to fetch Absences');
    }
  }

  List<AbsenceItem> get awaitingList => _awaitingList;
  List<AbsenceItem> get approvedList => _approvedList;
  List<AbsenceItem> get deniedList => _deniedList;
  List<AbsenceItem> get draftList => _draftList;
  List<AbsenceItem> get withdrawnList => withdrawnList;
  List<AbsenceItem> get fullAbsencesList => _fullAbsencesList;
  Future<List<String>> getAbsenceByUniqID(String absencesUniqID) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/absences/$absencesUniqID?expand=absenceAttachments',
      );
      // Parse response data into AbsenceItem model
      AbsenceItem absenceItem = AbsenceItem.fromJson(response.data);
      List<AbsenceAttachment> attachments = [];
      // Extract attachment IDs
      List<String> attachmentIds = [];

      if (response.data['absenceAttachments'] != null) {
        for (var attachment in response.data['absenceAttachments']) {
          if (attachment['links'] != null) {
            for (var link in attachment['links']) {
              if (link['name'] == "absenceAttachments" &&
                  link['rel'] == "self") {
                String? href = link['href'];
                if (href != null) {
                  List<String> parts = href.split('/');
                  if (parts.isNotEmpty) {
                    attachmentIds.add(parts.last);
                  }
                }
              }
            }
          }
        }
      }
      return attachmentIds;
    } catch (e) {
      throw Exception('Failed to fetch Absences');
    }
  }

  Future<dynamic> getAttachmentFileContent(
    String absencesUniqID,
    String absenceAttachmentsUniqID,
  ) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/absences/$absencesUniqID/child/absenceAttachments/$absenceAttachmentsUniqID/enclosure/fileContents',
      );
      // Extract Base64 data from response
      String base64String = response.data;
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.data);

        // Extract the Base64 string from response
        String base64String = responseData['fileContent'];

        // Decode Base64 to Uint8List
        Uint8List fileBytes = base64Decode(base64String);

        // TODO: Handle fileBytes (e.g., preview image, open PDF, save file)
      } else {}
      return response.data;
    } catch (e) {
      throw Exception('Failed to fetch attachment');
    }
  }

  Future<List<PlanBalanceModel>> getPlanBalances(String personId) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/planBalances',
        queryParameters: {'q': 'personId=$personId'},
      );

      if (response.data == null || !response.data.containsKey('items')) {
        return []; // Return empty list if 'items' is missing
      }

      return (response.data['items'] as List<dynamic>)
          .map(
            (json) => PlanBalanceModel.fromJson(json as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch Plan Balance');
    }
  }

  Future<List<PlanBalanceSummaryItemModel>> getPlanBalanceSummary(
    String personPlanEnrollmentId,
  ) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/planBalances/$personPlanEnrollmentId/child/planBalanceSummary',
      );

      if (response.data == null || response.data is! Map<String, dynamic>) {
        throw Exception('Invalid response format');
      }

      final Map<String, dynamic> data = response.data;

      if (!data.containsKey('items')) {
        return [];
      }

      final items = data['items'] as List;
      return items
          .map((item) => PlanBalanceSummaryItemModel.fromJson(item))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch Plan Balance Summary');
    }
  }

  Future<TaskDetailsModel?> getTask(
    String? taskStatus,
    int? identificationKey,
  ) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/tasks?assignment=CREATOR&totalResults=true&orderBy=createdDate:desc&onlyData=true&status=$taskStatus&identificationKey=$identificationKey',
        options: Options(validateStatus: (_) => true),
      );

      if (response.statusCode != 200) {
        throw Exception('Server responded with status ${response.statusCode}');
      }

      final data = response.data;
      if (data is Map<String, dynamic> && data['items'] is List) {
        final items = data['items'] as List;

        // Case 1: Only one item and it's FyiNotificationTask
        if (items.length == 1 &&
            items.first['taskDefinitionName'] == 'FyiNotificationTask') {
          return TaskDetailsModel.fromJson(items.first);
        }

        // Case 2: Look for AbsencesApprovalsTask
        for (var item in items) {
          if (item['taskDefinitionName'] == 'AbsencesApprovalsTask') {
            return TaskDetailsModel.fromJson(item);
          }
        }
      }
      return null; // No valid task found
    } catch (e) {
      throw Exception('Failed to fetch tasks: $e');
    }
  }

  Future<SingleTask> getTaskHistory(String taskNumber) async {
    try {
      // API call
      final response = await _dio.get(
        '${baseUrlEMSProvider}/tasks/$taskNumber/history?OnlyData=true&limit=1000&totalResults=true&orderBy=CreatedDate:desc&assignment=CREATOR',
      );

      return SingleTask.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to fetch task: $e');
    }
  }

  Future<dynamic> deleteAbsenceAttachment(
    String attachmentId,
    String absencesUniqID,
  ) async {
    try {
      final response = await _dio.delete(
        '${baseUrlEMSProvider}/absences/$absencesUniqID/child/absenceAttachments/$attachmentId',
      );
      if (response.statusCode == 200) {}
      return 'Attachment deleted successfully';
    } catch (e) {
      throw Exception('Failed to delete attachment: $e');
    }
  }

  Future<dynamic> uploadAttachment(
    List<Map<String, Object>> attachments,
    String absencesUniqID,
  ) async {
    try {
      FormData formData = FormData.fromMap({
        "DatatypeCode": "FILE",
        "CategoryName": "Medical",
        "Description": "Medical Certificate",
        "file": [
          for (var attachment in attachments)
            MultipartFile.fromBytes(
              (attachment["file"] as FileModel)
                  .bytes, // Extract bytes correctly
              filename: (attachment["file"] as FileModel).name, // Get file name
              contentType: MediaType.parse(
                _fixMimeType((attachment["file"] as FileModel).mimeType),
              ),
            ),
        ],
      });

      final response = await _dio.post(
        '${baseUrlEMSProvider}/absences/$absencesUniqID/child/absenceAttachments',
        data: formData, // Send FormData instead of raw attachments list
        options: Options(headers: {"Content-Type": "multipart/form-data"}),
      );
      return response.data;
    } catch (e) {
      throw Exception('Failed to upload attachment: $e');
    }
  }

  String _fixMimeType(String mimeType) {
    if (!mimeType.contains('/')) {
      // If the MIME type is just an extension like "png", correct it
      switch (mimeType.toLowerCase()) {
        case "png":
          return "image/png";
        case "jpg":
        case "jpeg":
          return "image/jpeg";
        case "gif":
          return "image/gif";
        case "pdf":
          return "application/pdf";
        case "txt":
          return "text/plain";
        case "mp4":
          return "video/mp4";
        default:
          return "application/octet-stream"; // Default for unknown types
      }
    }
    return mimeType; // If already correct, return as is
  }

  final Map<String, int> taskCountsByStatus = {
    'Awaiting': 0,
    'Approved': 0,
    'Rejected': 0,
    'Withdrawn': 0,
  };

  Future<TaskListResponse> getTasksForAssignToMe({String? status}) async {
    try {
      Map<String, dynamic> queryParams = {'limit': 25, 'forAbsences': true};

      if (status != null) {
        queryParams['status'] = status;
      }

      print('Fetching tasks with params: $queryParams');
      final response = await _dio.get(
        '${baseUrlEMSProvider}/tasks',
        queryParameters: queryParams,
      );
      print('Task List Response: ${response.data}');
      return TaskListResponse.fromJson(response.data);
    } catch (e) {
      print('Error in getTasks: $e');
      rethrow;
    }
  }

  /// Downloads and opens an absence attachment file (PDF or image) given the absence and attachment IDs.
  Future<void> downloadAndOpenAbsenceAttachment({
    required String absencesUniqID,
    required String absenceAttachmentsUniqID,
    required String fileName,
    required String contentType,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/absences/$absencesUniqID/child/absenceAttachments/$absenceAttachmentsUniqID/enclosure/fileContents',
        queryParameters: {'contentType': contentType, 'fileName': fileName},
        options: Options(responseType: ResponseType.bytes),
      );

      // Save file to disk (mobile)
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      await file.writeAsBytes(response.data);

      await OpenFile.open(filePath);
    } catch (e) {
      throw Exception('Failed to download or open attachment: $e');
    }
  }
}
