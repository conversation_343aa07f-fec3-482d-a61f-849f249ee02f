import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/parent/family/models/familyModel.dart';
import 'package:seawork/screens/parent/family/models/maritalStatusModel.dart';
import 'package:seawork/screens/parent/family/models/simpleFamily.dart';
import 'package:seawork/screens/parent/family/models/uploadTos3ResponseModel.dart';

@Injectable()
class FamilyRepository {
  final Dio _dio;

  FamilyRepository(this._dio);
  Future<FamilyModel> addUpdateFamily(FamilyModel body) async {
    try {
      final response = await _dio.post(
          '${baseUrlPMSProvider}/family/addUpdateFamily',
          data: body.toJson());
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return FamilyModel.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Family');
    } catch (e) {
      throw Exception('Failed to load Family');
    }
  }

  Future<UploadTos3> uploadTos3({required FormData formData}) async {
    try {
      final response = await _dio.post(
          '${baseUrlPMSProvider}/upload/UploadFileToS3',
          data: formData);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return UploadTos3.fromJson(response.data);
        }
      }
      throw Exception('Failed to load UploadTos3');
    } catch (e) {
      throw Exception('Failed to load UploadTos3');
    }
  }

  Future<MaritalStatus> addUpdateSpouse({int? familyId, int? parentId}) async {
    try {
      final response = await _dio.post(
          '${baseUrlPMSProvider}/family/addUpdateSpouse',
          data: {"familyId": familyId, "parentId": parentId});
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return MaritalStatus.fromJson(response.data);
        }
      }
      throw Exception('Failed to load MaritalStatus');
    } catch (e) {
      throw Exception('Failed to load MaritalStatus');
    }
  }

  Future<List<FamilyModel>> getFamilyDetailsByParentId(
      {String? fields,
      int? pageNumber,
      int? pageSize,
      int? id,
      String? orderBy,
      int? parentId}) async {
    try {
      final response = await _dio.get(
          '${baseUrlPMSProvider}/family/getFamilyDetailsByParentId',
          queryParameters: {
            "fields": fields,
            "pageNumber": pageNumber,
            "pageSize": pageSize,
            "id": id,
            "orderBy": orderBy,
            "parentId": parentId
          });
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => FamilyModel.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load Family');
    } catch (e) {
      throw Exception('Failed to load Family');
    }
  }

  Future<FamilyModel> removeFamilySpouseKid(
      {int? familyId, int? spouseId, int? kidId}) async {
    try {
      final response = await _dio.get(
          '${baseUrlPMSProvider}/family/removeFamilySpouseKid',
          queryParameters: {
            "familyId": familyId,
            "spouseId": spouseId,
            "kidId": kidId
          });
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return FamilyModel.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Family');
    } catch (e) {
      throw Exception('Failed to load Family');
    }
  }

  Future<List<SimpleFamily>> getFamilyDetailsByParentSpouseId(
      {int? parentId}) async {
    try {
      final response = await _dio.get(
          '${baseUrlPMSProvider}/family/getFamilyDetailsByParentSpouseId',);
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => SimpleFamily.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load Family');
    } catch (e) {
      throw Exception('Failed to load Family');
    }
  }

  Future<List<FamilyModel>> getFamilyDetailsByFamilyId(
      {String? fields,
      int? pageNumber,
      int? pageSize,
      int? id,
      String? orderBy,
      int? familyId,
      int? parentId}) async {
    try {
      final response = await _dio.get(
          '${baseUrlPMSProvider}/family/getFamilyDetailsByFamilyId',
          queryParameters: {
            "fields": fields,
            "pageNumber": pageNumber,
            "pageSize": pageSize,
            "id": id,
            "orderBy": orderBy,
            "familyId": familyId,
            "parentId": parentId
          });
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => FamilyModel.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load Family');
    } catch (e) {
      throw Exception('Failed to load Family');
    }
  }

  Future<List<FamilyModel>> getFamilySummaryByFamilyId({int? familyId}) async {
    try {
      final response = await _dio.get(
          '${baseUrlPMSProvider}/family/getFamilySummaryByFamilyId',
          queryParameters: {"familyId": familyId});
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => FamilyModel.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load Family');
    } catch (e) {
      throw Exception('Failed to load Family');
    }
  }
}
