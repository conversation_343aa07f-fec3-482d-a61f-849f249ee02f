import {
  Inject,
  Injectable,
  PipeTransform,
  UnauthorizedException,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';

@Injectable()
export class ValueTransformPipe implements PipeTransform {
  constructor(@Inject(REQUEST) private readonly request: Request) {}

  transform(value: any) {
    const url = this.request.url; // ✅ Get the request URL
    // // **1. Check Authorization Token**
    // if (!headers?.authorization || !headers.authorization.startsWith('Bearer ')) {
    //   throw new UnauthorizedException('Unauthorized: No valid token provided');
    // }

    // const token = headers.authorization.split(' ')[1]; // Extract token

    try {
      //   // **2. Validate Token & Extract User Info**
      //   const decodedToken = this.jwtService.decode(token) as { userId: string; role: string };

      //   if (!decodedToken || !decodedToken.userId) {
      //     throw new UnauthorizedException('Invalid Token');
      //   }

      // **3. Check If API Needs `userId` Injection**
      const excludeFindUserAPIs = ['absences']; // APIs to exclude
      const shouldInjectUserId = !excludeFindUserAPIs.some((api) =>
        url.includes(api),
      );

      if (shouldInjectUserId) {
        //  Object.assign(value, { finder: 'findByWord;PersonId=300000015297106' });
      }

      const onlyDataExcludedAPIs = ['emps'];
      if (!onlyDataExcludedAPIs.some((api) => url.includes(api))) {
        Object.assign(value, { onlyData: true, totalResults: true });
      } else {
        Object.assign(value, { totalResults: false });
      }

      return { ...value };
    } catch {
      throw new UnauthorizedException('Token verification failed');
    }
  }
}
