import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailhistorymodel.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_attachment_response_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_response_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_statusCount_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/documents_record_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/letter_request_types_model.dart';
import 'package:seawork/screens/employee/letterRequest/repositories/letter_request_repository.dart';
final letterRequestTypesRepositoryProvider = Provider((ref) {
  final dio = ref.watch(dioProvider);
  // final dioClient = DioClient(dio);
  return LetterRequestRepository(dio,baseUrlEMSProvider);
});

final LetterRequestTypesProvider =
    FutureProvider.autoDispose<List<LetterRequestTypesModel>>((ref) async {
  final service = ref.watch(letterRequestTypesRepositoryProvider);
  ref.keepAlive();
  return service.fetchDocumentsTypes();
});

final GetSingleDocumentRecordProvider = FutureProvider.autoDispose
    .family<DocumentResponse, int>((ref, personId) async {
  final service = ref.watch(letterRequestTypesRepositoryProvider);
  ref.keepAlive();
  return service.getSingleDocumentRecord(personId);
});

final GetDocumentRecordStatusProvider = FutureProvider.autoDispose<DocumentStatusCount>((ref) async {
  final service = ref.watch(letterRequestTypesRepositoryProvider);
  ref.keepAlive();
  return service.getDocumentRecordStatus();
});

final GetDocumentRecordStatusHistoryProvider = FutureProvider.autoDispose
    .family<TaskHistoryResponse, int>((ref, taskId) async {
  final service = ref.watch(letterRequestTypesRepositoryProvider);
  ref.keepAlive();
  return service.getTaskHistory(taskId);
});

final GetDocumentRecordAttachmentsProvider = FutureProvider.autoDispose
    .family<DocumentAttachmentsResponse, int>((ref, taskId) async {
  final service = ref.watch(letterRequestTypesRepositoryProvider);
  ref.keepAlive();
  return service.getDocumentAttachments(taskId);
});

final downloadLoadingProvider = StateProvider<bool>((ref) => false);

final downloadAttachmentProvider = Provider((ref) {
  final repo = ref.read(letterRequestTypesRepositoryProvider);
  final loadingNotifier = ref.read(downloadLoadingProvider.notifier);
  return (String url, String fileName) async {
    try {
      loadingNotifier.state = true;
      await repo.downloadAndSaveAttachment(
        url: url,
        fileName: fileName,
      );
    } finally {
      loadingNotifier.state = false;
    }
  };
});

class LetterRequestPaginationState {
  final List<DocumentRecord> records;
  final bool isLoading;
  final bool hasMore;
  final int offset;
  final String? error;
  final String? documentType;
  LetterRequestPaginationState({
    required this.records,
    this.isLoading = false,
    this.hasMore = true,
    this.offset = 0,
    this.error,
    this.documentType = "",
  });

  LetterRequestPaginationState copyWith({
    List<DocumentRecord>? records,
    bool? isLoading,
    bool? hasMore,
    int? offset,
    String? error,
    String? documentType,
  }) {
    return LetterRequestPaginationState(
      records: records ?? this.records,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      offset: offset ?? this.offset,
      error: error,
      documentType: documentType ?? this.documentType,
    );
  }
}

class LetterRequestPaginationNotifier
    extends StateNotifier<LetterRequestPaginationState> {
  final Ref _ref;
  final String status;
  static const int pageSize = 500;
  bool _isLoadingMore = false;
  DateTime? _startDate;
  DateTime? _endDate;
  LetterRequestPaginationNotifier(this._ref, this.status)
      : super(LetterRequestPaginationState(records: [])) {
    loadInitialRecords();
  }

  String _capitalizeEachWordPreserveUppercase(String value) {
    if (value.isEmpty) return value;
    return value.split(' ').map((word) {
      if (word.toUpperCase() == word) {
        return word;
      }
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }

  void search(String documentType) {
    final capitalizedType = _capitalizeEachWordPreserveUppercase(documentType);
    state = state.copyWith(documentType: capitalizedType);
    loadInitialRecords();
  }

  // void search(String documentType) {
  //   state = state.copyWith(documentType: documentType);
  //   loadInitialRecords();
  // }

  void reset() {
    state = LetterRequestPaginationState(
      records: [],
      documentType: "",
    );
    loadInitialRecords();
  }

  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    loadInitialRecords();
  }

  Future<void> clearDateFilters() async {
    _startDate = null;
    _endDate = null;

    loadInitialRecords();
  }

  Future<void> loadInitialRecords({onRefresh = false}) async {
    try {
      state = state.copyWith(isLoading: !onRefresh ? true : false, error: null);
      final repository = _ref.read(letterRequestTypesRepositoryProvider);
      final fetched = await repository.getPaginatedDocuments(
        300000015297106,
        offset: 0,
        limit: pageSize,
        searchQuery: state.documentType,
        startDate: _startDate,
        endDate: _endDate,status: status
      );

      state = LetterRequestPaginationState(
        records: fetched.items,
        isLoading: false,
        offset: 0,
        hasMore: fetched.hasMore,
        documentType: state.documentType,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load: $e',
      );
    }
  }

  Future<void> loadMoreRecords() async {
    if (_isLoadingMore || state.isLoading || !state.hasMore) return;

    _isLoadingMore = true;
    try {
      final offset = state.offset + pageSize;
      final repository = _ref.read(letterRequestTypesRepositoryProvider);
      final fetched = await repository.getPaginatedDocuments(
        300000015297106,
        offset: offset,
        limit: pageSize,
        searchQuery: state.documentType,
        startDate: _startDate,
        endDate: _endDate,
        status: status
      );

      state = state.copyWith(
        records: [...state.records, ...fetched.items],
        offset: offset,
        hasMore: fetched.hasMore,
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to load more: $e');
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> refresh() async {
    if (state.isLoading) return;
    state = LetterRequestPaginationState(records: []);
    await loadInitialRecords(onRefresh: true);
  }
}

// final letterRequestPaginationProvider = StateNotifierProvider<
//     LetterRequestPaginationNotifier, LetterRequestPaginationState>((ref) {
//   return LetterRequestPaginationNotifier(ref);
// });

final documentStatusProvider = StateProvider<String>((ref) => 'pending');

final letterRequestPendingProvider = StateNotifierProvider<LetterRequestPaginationNotifier, LetterRequestPaginationState>(
  (ref) => LetterRequestPaginationNotifier(ref, "PENDING"),
);

final letterRequestApprovedProvider = StateNotifierProvider<LetterRequestPaginationNotifier, LetterRequestPaginationState>(
  (ref) => LetterRequestPaginationNotifier(ref, "APPROVED"),
);

// final letterRequestRejectedProvider = StateNotifierProvider<LetterRequestPaginationNotifier, LetterRequestPaginationState>(
//   (ref) => LetterRequestPaginationNotifier(ref, "REJECTED"),
// );

// final letterRequestWithdrawProvider = StateNotifierProvider<LetterRequestPaginationNotifier, LetterRequestPaginationState>(
//   (ref) => LetterRequestPaginationNotifier(ref, "WITHDRAW"),
// );
final selectedOptionProvider = StateProvider<String?>((ref) => null);
final letterRequestDateRangeProvider = StateProvider<Map<String, DateTime?>?>((ref) => null);