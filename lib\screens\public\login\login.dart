import 'dart:convert';
import 'dart:math';

import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/router.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:go_router/go_router.dart';

class Login extends StatefulWidget {
  static var tag = "login";

  const Login({Key? key}) : super(key: key);

  @override
  _LoginState createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final _formKey = GlobalKey<FormState>();
  String? email;
  String? password;
  bool _isObscured = true;
  String? codeVerifier;
  String? sessionToken;
  late AppLinks _appLinks;
  bool _isProcessingToken = false;
  bool _showLoginUI = true;

  @override
  void initState() {
    print('Initializing Login widget');
    super.initState();
    _initializeAppLinks();

    // Delay to ensure widget is mounted
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check for token in URL if running on web
      if (kIsWeb) {
        final uri = Uri.base;
        final token = uri.queryParameters['token'];
        if (token != null && !_isProcessingToken) {
          print('Token found in URL: $token');
          print('Token received from deep link: $token');
          _isProcessingToken = true;
          setState(() {
            _showLoginUI = false;
          });
          getAccessToken();
        }
      }
    });
  }

  Future<void> _initializeAppLinks() async {
    // Initialize app links
    _appLinks = AppLinks();
    print('AppLinks initialized');

    // Listen for deep links
    _appLinks.uriLinkStream.listen((uri) async {
      print('Received deep link: $uri');
      // Check if the URI contains a token parameter
      final token = uri.queryParameters['token'];
      if (token != null && !_isProcessingToken && mounted) {
        print('Token received from deep link: $token');
        _isProcessingToken = true;
        setState(() {
          _showLoginUI = false;
        });
        await getAccessToken();
      }
    });
  }

  String generateCodeVerifier() {
    final Random random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url
        .encode(bytes)
        .replaceAll('=', '')
        .replaceAll('+', '-')
        .replaceAll('/', '_');
  }

  String get redirectUri {
    if (kIsWeb) {
      final origin = Uri.base.origin;
      return '$origin/oauthredirect';
    } else {
      // For mobile, use the custom URI scheme
      return 'app.sea://oauthredirect';
    }
  }

  Future<void> _launchUrl(String url) async {
    print('Launching URL: $url');
    launchUrl(
      Uri.parse(url),
      mode: LaunchMode.externalApplication,
    );
  }

  Future<void> _handleMicrosoftLogin() async {
    try {
      print('Starting Microsoft login flow');

      // Generate code verifier
      codeVerifier = generateCodeVerifier();
      print('Generated code verifier: $codeVerifier');

      // Save code verifier for later use
      await PreferencesUtils.setString(
          PreferencesUtils.CODE_VERIFIER, codeVerifier!);

      // Prepare login URL with platform-specific redirect
      final loginUrl =
          '${baseUrlAuthProvider}/auth/login?codeVerifier=$codeVerifier&redirect=${Uri.encodeComponent(redirectUri)}';
      print('Login URL: $loginUrl');

      // Launch the URL
      _launchUrl(loginUrl);
    } catch (e) {
      print('Error during login: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Login failed. Please try again.')),
      );
    }
  }

  Future<void> getAccessToken() async {
    try {
      print('Retrieving access token from backend');
      codeVerifier =
          await PreferencesUtils.getString(PreferencesUtils.CODE_VERIFIER);

      if (codeVerifier == null) {
        print('Error: Code verifier not found');
        return;
      }

      print('Code verifier: $codeVerifier');

      final uri = Uri.parse(
          '${baseUrlAuthProvider}/auth/access-token?codeVerifier=$codeVerifier');
      print('Access token URL: $uri');

      final response = await http.get(uri, headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });

      /**   tokenData: tokenData,
      user: user,
      userProfiles: userProfiles, */

      if (response.statusCode == 200) {
        print('Access token retrieved successfully');
        print('Response body: ${response.body}');
        final tokenData = json.decode(response.body);
        print('Token data: $tokenData');
        sessionToken = tokenData['accessToken'];
        final user = tokenData['user'];
        final userProfiles = tokenData['userProfiles'];
        await handleSessionToken(sessionToken!, user, userProfiles);
      } else {
        print('Failed to get access token. Status: ${response.statusCode}');
        print('Response: ${response.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text('Failed to get access token. Please try again.')),
          );
          setState(() {
            _showLoginUI = true;
            _isProcessingToken = false;
          });
        }
      }
    } catch (e) {
      print('Error getting access token: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error getting access token. Please try again.')),
        );
        setState(() {
          _showLoginUI = true;
          _isProcessingToken = false;
        });
      }
    }
  }

  Future<void> handleSessionToken(String token,
      [Map<String, dynamic>? user, List<dynamic>? userProfiles]) async {
    print('Handling session token');
    await PreferencesUtils.setString(PreferencesUtils.SESSION_TOKEN, token);
    await PreferencesUtils.setString(PreferencesUtils.USER, json.encode(user));
    await PreferencesUtils.setString(
        PreferencesUtils.USER_PROFILES, json.encode(userProfiles));
    print('Session token saved: $token');
    print('User saved: $user');
    print('User profiles saved: $userProfiles');

      if (userProfiles != null &&
          userProfiles.isNotEmpty &&
          userProfiles.first['Type'] == 'PMS') {
        appRouter.go('/profile-dashboard');
      } else {
        appRouter.go('/dashboard');
        print('Navigating to Dashboard');
      } 
  }

  @override
  void dispose() {
    print('Disposing Login widget');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Show loading indicator while processing token
    if (!_showLoginUI) {
      return Scaffold(
        body: Stack(
          children: [
            // Background Image
            Positioned.fill(
              child: CustomSvgImage(
              imageName:'loginbackground'
              // 'assets/images/loginbackground.svg',
              // fit: BoxFit.cover,
            ),
            ),
            // Loading indicator
            Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.whiteColor),
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          Positioned.fill(
            child: CustomSvgImage(
              imageName:'loginbackground'
              // 'assets/images/loginbackground.svg',
              // fit: BoxFit.cover,
            ),
          ),
          // Overlay Content
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                // Logo
               CustomSvgImage(
                imageName: 'logoz', // Replace with your SVG logo asset
                  height: 100,
                  width: 100,
                ),
                const SizedBox(height: 20),

                // Welcome Text
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.transparencylogin,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      OpenSansText(
                        "Hello",
                        textAlign: TextAlign.center,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.whiteColor,
                      ),
                       OpenSansText(
                        "Welcome back new",
                        textAlign: TextAlign.center,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.whiteColor,
                      ),
                      const SizedBox(height: 24),

                      // Input Field
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.whiteColor.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: TextField(
                          decoration: InputDecoration(
                            labelText: "Email/Mobile number",
                            labelStyle: GoogleFonts.openSans(color: AppColors.whiteColor),
                            contentPadding:
                                const EdgeInsets.symmetric(horizontal: 15),
                            border: InputBorder.none,
                          ),
                          style: GoogleFonts.openSans(color: AppColors.whiteColor),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Continue Button
                      ElevatedButton(
                        onPressed: () {
                          context.push('/dashboard');
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.whiteColor,
                          minimumSize: const Size(double.infinity, 56),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8)),
                        ),
                        child: DmSansText(
                          "Continue",
                            color: AppColors.blackColor,
                            fontSize: 16,  
                        ),
                      ),
                      const SizedBox(height: 12),

                      // OR Divider
                      DmSansText(
                        "or",
                       color: AppColors.whiteColor, fontSize: 12),
                      const SizedBox(height: 12),

                      // Social Login Buttons
                      _socialButton("Continue with Microsoft"),
                      _socialButton("Continue with OTP"),
                      _socialButton("Continue with UAE Pass"),
                    ],
                  ),
                ),
              ]),
            ),
          )
        ],
      ),
    );
  }

  Widget _socialButton(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: OutlinedButton.icon(
        onPressed: () {
          print('Button pressed: $text');
          if (text == "Continue with Microsoft") {
            _handleMicrosoftLogin();
          } else if (text == "Continue with OTP") {
            context.push('/parent-mobile-verification');
          } else if (text == "Continue with UAE Pass") {
            // Handle UAE Pass login
          }
        },
        label: DmSansText(
          text,
            color: AppColors.whiteColor,
            fontSize: 16,
            fontFamily: 'DMSans',
        ),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: AppColors.whiteColor),
          minimumSize: const Size(double.infinity, 56),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}