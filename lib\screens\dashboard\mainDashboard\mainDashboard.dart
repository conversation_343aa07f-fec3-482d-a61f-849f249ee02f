import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/permission/permissions.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/image/renderNavigationPages.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/screens/account/profile/repository/profileRepository.dart';
import 'package:seawork/screens/dashboard/parentDashboard/components/parentWidget.dart';
import 'package:seawork/screens/employee/employee/components/employeeWidget.dart';
import 'package:seawork/screens/dashboard/dashboard/components/eventWidget.dart';
import 'package:seawork/screens/dashboard/dashboard/components/upcomingActivityWidget.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MainDashboard extends ConsumerStatefulWidget {
  static var tag = "MainDashboard";

  const MainDashboard({super.key});

  @override
  ConsumerState<MainDashboard> createState() => _MainDashboardState();
}

class _MainDashboardState extends ConsumerState<MainDashboard> {
  String? userName = '';
    late final PermissionChecker _permissionChecker;

  List<Map<String, dynamic>> recentlyUsedItems = [];
  bool _isRecentlyUsedExpanded = true;
 Future<void> _fetchAndStoreEmployeeDetails() async {
    try {
      final repository = ref.read(profileRepositoryProvider);
      final employeeDetails = await repository.getProfileDetails();

      if (employeeDetails != null) {
        final jsonString = jsonEncode(employeeDetails.toJson());
        await PreferencesUtils.setEmployeeDetails(jsonString);
        print('Employee details stored locally');
      }
    } catch (e) {
      print('Error fetching employee details: $e');
    }
  }
  @override
  void initState() {
    super.initState();
    print('Dashboard mounted, checking session...');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkSession();
        _loadRecentlyUsedItems();
        _fetchAndStoreEmployeeDetails();
    _permissionChecker = PermissionChecker();

      }
    });
  }

 

  Future<void> _checkSession() async {
    print('Checking session in Dashboard...');
    final sessionToken =
        await PreferencesUtils.getString(PreferencesUtils.SESSION_TOKEN);

    final userJson = await PreferencesUtils.getString(PreferencesUtils.USER);
    if (userJson != null && userJson.isNotEmpty) {
      try {
        final Map<String, dynamic> userMap = jsonDecode(userJson);
        final userName = userMap['name'];
        print('User name: $userName');

        setState(() {
          this.userName = userName ?? 'Guest';
        });
      } catch (e) {
        print('Failed to decode user JSON: $e');
      }
    } else {
      print('User JSON is null, navigating to login...');
      context.go('/login');
    }
  }

  Future<void> _loadRecentlyUsedItems() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? jsonString = prefs.getString('recentlyUsedItems');
    if (jsonString != null) {
      List<dynamic> decodedList = jsonDecode(jsonString);
      List<Map<String, dynamic>> items = List<Map<String, dynamic>>.from(
          decodedList.map((item) => Map<String, dynamic>.from(item)));

      setState(() {
        recentlyUsedItems = items;
      });
    }
  }

  void _addRecentlyUsed(String item) async {
    setState(() {
      if (!recentlyUsedItems.any((element) => element['item'] == item)) {
        if (recentlyUsedItems.length >= 4) {
          recentlyUsedItems.removeAt(0);
        }

        recentlyUsedItems.add({
          'item': item,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
        print('Added recently used item: $item');
      }
    });

    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('recentlyUsedItems', jsonEncode(recentlyUsedItems));
  }

  void _navigateToItem(String label) {
    IconNavigationHelper.navigateToScreen(
      context: context,
      label: label,
      onIconClicked: _addRecentlyUsed,
    );
  }

  void _openSearchScreen() {
    context.push('/search', extra: {
      'onItemSelected': _navigateToItem,
      'addRecentlyUsed': _addRecentlyUsed,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      backgroundColor: AppColors.secondaryColor,
      body: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 320,
              child: CustomPngImage(
                imageName: 'dashboardbg',
                boxFit: BoxFit.cover,
              ),
            ),
            Column(
              children: [
                SafeArea(
                  bottom: false,
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: AppColors.primaryColor,
                          radius: 22,
                          child: CircleAvatar(
                            backgroundColor: AppColors.whiteColor,
                            radius: 19,
                            child: GestureDetector(
  onTap: () {
    context.push('/my-profile');
  },
  child: ClipOval(
    child: SizedBox(
      width: 35,
      height: 35,
      child: CustomJpgImage(
        imageName: "profileimage",
      ),
    ),
  ),
)
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DmSansText(
                                  userName ?? 'User Name',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 2),
                                OpenSans400Large(
                                  12,
                                  'Welcome',
                                  AppColors.viewColor,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            InkWell(
                              onTap: _openSearchScreen,
                              child: CustomSvgImage(
                                imageName: "ic_dashboardsearch",
                                color: AppColors.viewColor,
                              ),
                            ),
                            SizedBox(width: 24),
                            Stack(
                              clipBehavior: Clip.none,
                              alignment: Alignment.center,
                              children: [
                               GestureDetector(
                                  onTap: () {
                                    context.push('/notifications');
                                  },
                                  child: CustomSvgImage(
                                    imageName: "ic_notification",
                                    color: AppColors.viewColor,
                                  ),
                                ),
                              
                             
                              ],
                            ),
                            SizedBox(width: 24),
                            InkWell(
                              onTap: () {
                                context.push('/help');
                              },
                              child: CustomSvgImage(
                                imageName: "help",
                                color: AppColors.viewColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        if (recentlyUsedItems.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Container(
                              decoration: BoxDecoration(
                                color: AppColors.whiteColor,
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.boxshadowcolor.withOpacity(
                                      0.25,
                                    ),
                                    blurRadius: 10,
                                    offset: Offset(0, 0),
                                  ),
                                ],
                                border: Border.all(
                                  color: AppColors.lightgraygreen,
                                  width: 0.5,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      20,
                                      12,
                                      20,
                                      6,
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        DMSans600Small(
                                          14,
                                          'Recently used',
                                          AppColors.dashboardheading,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                    ),
                                    child: Divider(
                                      color: AppColors.calenderdivColor,
                                      thickness: 0.4,
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                    ),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child: _buildRecentlyUsedSection(),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                ],
                              ),
                            ),
                          ),
                        SizedBox(height: 12),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            children: [
                              UpcomingActivityWidget(),
                              SizedBox(height: 12),
if (_permissionChecker.isVisible(['EMS']))
  EmployeeWidget(onIconClicked: _addRecentlyUsed),
                              SizedBox(height: 12),
                               if(_permissionChecker.isVisible(['EMS'])) EventWidget(),
                              SizedBox(height: 12),
                              if (_permissionChecker.isVisible(['PMS']))
  ParentWidget(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (index) {}),
    );
  }

  Widget _buildRecentlyUsedSection() {
    final List<Map<String, dynamic>> limitedItems = 
        recentlyUsedItems.take(4).toList();
    final List<Map<String, String>> allIcons = IconNavigationHelper.getAllIcons();

    return GridView.builder(
      padding: const EdgeInsets.only(top: 8),
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        mainAxisSpacing: 16,
        crossAxisSpacing: 8,
        childAspectRatio: 0.85,
      ),
      itemCount: limitedItems.length,
      itemBuilder: (context, index) {
        final item = limitedItems[index]['item'];
        String iconPath = 'assets/images/default_icon.svg';
        
        for (final icon in allIcons) {
          if (icon['label']?.contains(item) ?? false) {
            iconPath = icon['path']!;
            break;
          }
        }

        return _buildRecentlyUsedIcon(
          iconPath,
          item,
          context,
        );
      },
    );
  }

  Widget _buildRecentlyUsedIcon(
    String imagePath,
    String label,
    BuildContext context,
  ) {
    return Material(
    color: AppColors.transparentColor,
child: InkWell(
  borderRadius: BorderRadius.circular(8),
  splashColor: AppColors.microinteraction,
  highlightColor: AppColors.microinteraction,
  onTap: () {
    IconNavigationHelper.navigateToScreen(
      context: context,
      label: label,
      onIconClicked: _addRecentlyUsed,
    );
  },
  child: Column(
    mainAxisAlignment: MainAxisAlignment.start,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Container(
        height: 40,
        width: 40,
        padding: const EdgeInsets.all(5),
        child: SvgPicture.asset(
          imagePath,
          fit: BoxFit.contain,
          width: 32,
          height: 32,
        ),
      ),
      const SizedBox(height: 6),
      SizedBox(
        width: 72,
        child: OpenSansText(
          label,
          textAlign: TextAlign.center,
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: AppColors.blackColor,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          fontheight: 1.2,
        ),
      ),
    ],
  ),
));
  }
}