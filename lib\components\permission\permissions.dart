import '../../data/preferencesUtils.dart';

/// Permissions utility class for handling user profile type checking
/// This class provides reusable functions to determine user permissions
/// based on their profile types (PMS, EMS, etc.)
/// User profiles are automatically fetched from SharedPreferences
class PermissionChecker {
  List<dynamic> _userProfiles;
  PermissionChecker() : _userProfiles = [] {
    _initialize();
  }

  Future<void> _initialize() async {
    _userProfiles = (await PreferencesUtils.getUserProfiles()) ?? [];
  }

  /// Checks user profiles and returns appropriate permission flag
  /// Returns: 'pmsUser', 'emsUser', 'multiUser', or 'unknownUser'
  static Future<String> permissionChecker() async {
    final userProfiles = await PreferencesUtils.getUserProfiles();

    if (userProfiles == null || userProfiles.isEmpty) {
      return 'unknownUser';
    }

    bool hasPMS = false;
    bool hasEMS = false;

    for (var profile in userProfiles) {
      if (profile is Map<String, dynamic> && profile.containsKey('Type')) {
        String? type = profile['Type']?.toString();

        if (type == 'PMS') {
          hasPMS = true;
        } else if (type == 'EMS') {
          hasEMS = true;
        }
      }
    }

    if (hasPMS && hasEMS) {
      return 'multiUser';
    } else if (hasPMS) {
      return 'pmsUser';
    } else if (hasEMS) {
      return 'emsUser';
    } else {
      return 'unknownUser';
    }
  }

  /// Check if user is PMS type only
  static Future<bool> isPMSUser() async {
    final result = await permissionChecker();
    return result == 'pmsUser';
  }

  /// Check if user is EMS type only
  static Future<bool> isEMSUser() async {
    final result = await permissionChecker();
    return result == 'emsUser';
  }

  /// Check if user has both PMS and EMS types
  static Future<bool> isMultiUser() async {
    final result = await permissionChecker();
    return result == 'multiUser';
  }

  bool isVisible(List<String> tenants)  {
    final userProfiles = _userProfiles;
    if (userProfiles == null || userProfiles.isEmpty) {
      return false;
    }

    for (var profile in userProfiles) {
      if (profile is Map<String, dynamic> && profile.containsKey('Type')) {
        String? type = profile['Type']?.toString();
        if (tenants.contains(type)) {
          return true;
        }
      }
    }
    return false;
  }


  /// Get all unique profile types from user profiles
  static Future<List<String>> getUserTypes() async {
    final userProfiles = await PreferencesUtils.getUserProfiles();

    if (userProfiles == null || userProfiles.isEmpty) {
      return [];
    }

    Set<String> types = {};

    for (var profile in userProfiles) {
      if (profile is Map<String, dynamic> && profile.containsKey('Type')) {
        String? type = profile['Type']?.toString();
        if (type != null && type.isNotEmpty) {
          types.add(type);
        }
      }
    }

    return types.toList();
  }
}