import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path/path.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/parent/parent/constants/parentConstants.dart';
import 'package:seawork/screens/parent/parent/models/app_parent_details_model.dart';
import 'package:seawork/screens/parent/parent/models/logged_in_parent_menus_data_model.dart';
import 'package:seawork/screens/parent/parent/models/ocrUploadResultModel.dart';
import 'package:seawork/screens/parent/parent/models/parentDetails.dart';
import 'package:seawork/screens/parent/parent/models/parentEmployeeNameModel.dart';
import 'package:seawork/screens/parent/parent/models/parentOtpByPassViewModelModel.dart';
import 'package:seawork/screens/parent/parent/models/parentProfileModel.dart';
import 'package:seawork/screens/parent/parent/models/parentModel.dart';
import 'package:seawork/screens/parent/parent/models/parentDocumentDetailsModel.dart';
import 'package:seawork/screens/parent/parent/models/parentProfileVMModel.dart';
import 'package:seawork/screens/parent/parent/models/parentProfileWizardUpdateModel.dart';
import 'package:seawork/screens/parent/parent/models/parentProfileUpdateModel.dart';
import 'package:http_parser/http_parser.dart';
import 'package:seawork/screens/parent/parent/models/parentProfileWizardUpdateSingleModel.dart';
import 'package:seawork/screens/parent/parent/models/verifyExistingParentOtpResponseModel.dart';

@Injectable()
class ParentRepository {
  final Dio _dio;

  ParentRepository(this._dio);
  Future<AppParentDetailsModel> postAppParentDetails({int? parentId}) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/dashboard/getAppParentDetails',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return AppParentDetailsModel.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentProfile');
    } catch (e) {
      throw Exception('Failed to load ParentProfile');
    }
  }

  Future<Parent> createParent(Parent body) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/createParent',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<Parent> updateParent(Parent body) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/updateParent',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<ParentDocumentDetails> updateParentDocumentDetails(
    ParentDocumentDetails body,
  ) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/updateParentDocumentDetails',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return ParentDocumentDetails.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentDocumentDetails');
    } catch (e) {
      throw Exception('Failed to load ParentDocumentDetails');
    }
  }

  Future<ParentProfileVM> updateParentProfile(ParentProfileVM body) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/updateParentProfile',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return ParentProfileVM.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentProfileVM');
    } catch (e) {
      throw Exception('Failed to load ParentProfileVM');
    }
  }

  Future<Parent> deleteParent(Parent body) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/deleteParent',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<List<ParentProfile>> getAllParents({
    String? fields,
    int? pageNumber,
    int? pageSize,
    int? id,
    String? orderBy,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/getAllParents',
        queryParameters: {
          "fields": fields,
          "pageNumber": pageNumber,
          "pageSize": pageSize,
          "id": id,
          "orderBy": orderBy,
        },
      );
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => ParentProfile.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load ParentProfile');
    } catch (e) {
      throw Exception('Failed to load ParentProfile');
    }
  }

  Future<Parent> getAllParentDetailsById({int? parentId}) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/getAllParentDetalsById',
      );
      if (response.statusCode == 200) {
        return Parent.fromJson(response.data as Map<String, dynamic>);
      }
      throw Exception('Failed to load ParentProfileVM');
    } catch (e) {
      throw Exception('Failed to load ParentProfileVM');
    }
  }

  Future<ParentProfile?> getParentProfileDetailsById({int? parentId}) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/getAllParentDetalsById',
        
      );
      print('Raw response: ${response.data}');

      if (response.statusCode == 200) {
        final profilesJson = response.data['parentProfiles'] as List<dynamic>?;

        if (profilesJson != null && profilesJson.isNotEmpty) {
          return ParentProfile.fromJson(
            profilesJson[0] as Map<String, dynamic>,
          );
        } else {
          // No profiles found
          return null;
        }
      } else {
        throw Exception('Failed to load parent profiles');
      }
    } catch (e) {
      throw Exception('Failed to load ParentProfileVM: $e');
    }
  }

  Future<List<ParentProfileVM>> getAllReviewedParentDetailsById({
    int? parentId,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/getAllReviewedParentDetalsById',
      );
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => ParentProfileVM.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load ParentProfileVM');
    } catch (e) {
      throw Exception('Failed to load ParentProfileVM');
    }
  }

  Future<List<ParentProfileVM>> getAllNotVerifiedParents({
    String? fields,
    int? pageNumber,
    int? pageSize,
    int? id,
    String? orderBy,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/getAllNotVeryfiedParents',
        queryParameters: {
          "fields": fields,
          "pageNumber": pageNumber,
          "pageSize": pageSize,
          "id": id,
          "orderBy": orderBy,
        },
      );
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => ParentProfileVM.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load ParentProfileVM');
    } catch (e) {
      throw Exception('Failed to load ParentProfileVM');
    }
  }

  Future<Parent> verifyParentEmiratesId({
    String? emiratesNo,
    String? requestId,
    String? oTP,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/verifyParentEmiratesId',
        queryParameters: {
          "emiratesNo": emiratesNo,
          "requestId": requestId,
          "oTP": oTP,
        },
      );
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<Parent> checkSpouseEmiratesId({
    String? emiratesNo,
    int? familyId,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/checkSpouseEmiratesId',
        queryParameters: {"emiratesNo": emiratesNo, "familyId": familyId},
      );
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<Parent> checkParentEmiratesId({
    String? emiratesNo,
    int? parentId,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/checkParentEmiratesId',
        queryParameters: {"emiratesNo": emiratesNo},
      );
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<Parent> checkSpouseEmiratesIdByPass({
    String? emiratesNo,
    int? familyId,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/checkSpouseEmiratesIdByPass',
        queryParameters: {"emiratesNo": emiratesNo, "familyId": familyId},
      );
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<Parent> verifyParentEmiratesIdByPass({
    String? emiratesNo,
    String? requestId,
    String? oTP,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/verifyParentEmiratesIdByPass',
        queryParameters: {
          "emiratesNo": emiratesNo,
          "requestId": requestId,
          "oTP": oTP,
        },
      );
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return Parent.fromJson(response.data);
        }
      }
      throw Exception('Failed to load Parent');
    } catch (e) {
      throw Exception('Failed to load Parent');
    }
  }

  Future<ParentProfileWizardUpdate> updateParentProfileForWizard(
    ParentProfileWizardUpdate body,
  ) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/updateParentProfileForWizard',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return ParentProfileWizardUpdate.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentProfileWizardUpdate');
    } catch (e) {
      throw Exception('Failed to load ParentProfileWizardUpdate');
    }
  }

  Future<ParentProfileUpdate> parentProfileUpdate(
    ParentProfileUpdate body,
  ) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/parentProfileUpdate',
        data: body.toJson(),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return ParentProfileUpdate.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentProfileUpdate');
    } catch (e) {
      throw Exception('Failed to load ParentProfileUpdate');
    }
  }

  Future<List<ParentProfileVM>> getAllParentDetails() async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parent/getllParentDetails',
        queryParameters: {},
      );
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => ParentProfileVM.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load ParentProfileVM');
    } catch (e) {
      throw Exception('Failed to load ParentProfileVM');
    }
  }

  Future<LoggedInParentMenusDataModel> getLoggedInParentMenusData({
    int? parentId,
    int? familyId,
    int? kidId,
  }) async {
    final queryParams = Map.fromEntries([
      if (familyId != null) MapEntry('familyId', familyId),
      if (kidId != null) MapEntry('kidId', kidId),
    ]);
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/parentmenu/getLoggedInParentMenusData',
        queryParameters: queryParams,
      );
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic>) {
          return LoggedInParentMenusDataModel.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentProfile');
    } catch (e) {
      throw Exception('Failed to load ParentProfile');
    }
  }


    Future<ParentDetails> updateSingleParentProfileForWizard(
    ParentProfileWizardUpdateSingle body,
  ) async {
    try {
      final response = await _dio.post(
        '${baseUrlPMSProvider}/parent/updateParentProfileForWizard_2',
        data: body.toJson(),options: Options(headers: {'Accept': 'application/json'})
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return ParentDetails.fromJson(response.data);
        }
      }
      throw Exception('Failed to load ParentProfileWizardUpdate');
    } catch (e) {
      throw Exception('Failed to load ParentProfileWizardUpdate');
    }
  }

  Future<ParentDetails> getParentDetailsById({int? parentId}) async {
      final prefs = await SharedPreferences.getInstance();
     try {
    final response = await _dio.get('${baseUrlPMSProvider}/parent/GetAllParentDetalsById',options: Options(headers: {'Accept': 'application/json'}));
    if (response.statusCode == 200 && response.data != null) {
        final Map<String, dynamic> responseData = response.data as Map<String, dynamic>;
        await prefs.setString('parentAllDetails', json.encode(responseData));
        print('API Response Data: $responseData'); // Print the raw parsed data for debugging
        return ParentDetails.fromJson(responseData);
      } else {
        throw Exception('Failed to load Parent details: ${response.statusCode}');
      }
    } on DioException catch (e) {
  throw Exception('Failed to load Parent details due to Dio error: ${e.message}');
  } catch (e) {
    print('e is ${e}' );

    throw Exception('Failed to load ParentProfileVM');
  }

  }

  Future<OcrUploadResult> uploadFileToS3WithOcr(File file) async {
    final String url = '${baseUrlPMSProvider}/parent/upload/UploadFileToS3WithOcr';

    final headers = {
      'Accept': 'application/json',
    };

    final fileName = basename(file.path);
    final fileExtension = extension(file.path).toLowerCase();

    // Determine MIME type based on file extension
    MediaType mediaType;
    if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
      mediaType = MediaType('image', 'jpeg');
    } else if (fileExtension == '.png') {
      mediaType = MediaType('image', 'png');
    } else if (fileExtension == '.pdf') {
      mediaType = MediaType('application', 'pdf');
    } else {
      throw Exception('Unsupported file type');
    }

    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        file.path,
        filename: fileName,
        contentType: mediaType,
      ),
    });

    try {
      final response = await _dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;

        final extractedData = data['data'];
        final fileKey = data['fileKeyName'] ?? '';
        print('extractedData is ${extractedData}');
        final buffer = StringBuffer();

      void addIfNotEmpty(String label, String? value) {
        if (value != null && value.trim().isNotEmpty) {
          buffer.writeln('$label: ${value.trim()}');
        }
      }

      addIfNotEmpty('Name', extractedData['Name']);
      addIfNotEmpty('Arabic Name', extractedData['Name Arabic']);
      addIfNotEmpty('ID Number', extractedData['ID__Number']);
      addIfNotEmpty('Nationality', extractedData['Nationality']);
      addIfNotEmpty('Exp', extractedData['MR Expiry Date'] ?? extractedData['Expiry Date']);
      addIfNotEmpty('Gender', extractedData['MR Gender'] ?? extractedData['Gender']);
      addIfNotEmpty('Passport NO', extractedData['MR Passport NO']);
      addIfNotEmpty('Expiry', extractedData['MR Date of Expiry']);
      addIfNotEmpty('Authority', extractedData['Issuing Authority']);
      // addIfNotEmpty('Place of Birth', extractedData['Place of Birth']);
      print('--- Extracted OCR Data ---');
      print(buffer.toString());
      print('--------------------------');

      final resultText = buffer.toString().trim();
      return OcrUploadResult(
        fileKeyName: fileKey,
        extractedData: resultText,
      );
      } else {
        throw Exception('Upload failed: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Upload error: $e');
    }
  }

  Future<Map<String, dynamic>> uploadFileToS3(PlatformFile file) async {
    final String url = '${baseUrlPMSProvider}/parent/upload/UploadFileToS3';

     final filePath = file.path;
  if (filePath == null) {
    throw Exception('File path is null');
  }
final fileName = basename(filePath);
  final fileExtension = extension(filePath).toLowerCase();
    // Determine MIME type based on file extension
    MediaType mediaType;
    if (fileExtension == '.jpg' || fileExtension == '.jpeg') {
      mediaType = MediaType('image', 'jpeg');
    } else if (fileExtension == '.png') {
      mediaType = MediaType('image', 'png');
    } else if (fileExtension == '.pdf') {
      mediaType = MediaType('application', 'pdf');
    } else {
      throw Exception('Unsupported file type');
    }

    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        filePath,
        filename: fileName,
        contentType: mediaType,
      ),
    });

    try {
      final response = await _dio.post(
        url,
        data: formData,
        // options: Options(headers: headers),
      );
      return response.data;
    } catch (e) {
      throw Exception('Upload error: $e');
    }
  }

  Future<ParentOtpByPassViewModelModel> sendExistingParentMobileOTPByPass({String? phoneNumber, int? parentId}) async {
     try {
    final response = await _dio.get('${baseUrlPMSProvider}/authenticate/sendExistingParentMobileOTPByPass', queryParameters: { "phoneNumber": phoneNumber});
    if (response.statusCode == 200) {
      if (response.data is Map<String, dynamic>) {
        return ParentOtpByPassViewModelModel.fromJson(response.data);
      }
      }
    throw Exception('Failed to send otp');
  } catch (e) {
    throw Exception('Failed to send otp');
  }
  }

  Future<ParentOtpByPassViewModelModel> sendExistingParentEmailOTP({String? email, int? parentId}) async {
     try {
    final response = await _dio.get('${baseUrlPMSProvider}/authenticate/sendExistingParentEmailOTP', queryParameters: { "email": email});
    if (response.statusCode == 200) {
      if (response.data is Map<String, dynamic>) {
        return ParentOtpByPassViewModelModel.fromJson(response.data);
      }
      }
    throw Exception('Failed to send otp');
  } catch (e) {
    throw Exception('Failed to send otp');
  }
  }

  Future<VerifyExistingParentOtpResponseModel> verifyOtp({
  required String otp,
  required String emailOrPhone,
  required int parentId,
  String? requestId, // If this is passed, assume mobile verification
  }) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    if (requestId != null) {
      // Mobile OTP verification
      final response = await _dio.get(
        '${baseUrlPMSProvider}/authenticate/verifyExistingParentMobileOTPByPass',
        queryParameters: {
          "otp": otp,
          "phoneNumber": emailOrPhone,
          "requestId": requestId,
        },
      );

      if (response.data is Map<String, dynamic>) {
        final model = VerifyExistingParentOtpResponseModel.fromJson(response.data);

        // 🔄 Update phone number in local storage
        final String? existingData = prefs.getString('parentDetails');
        if (existingData != null) {
          try {
            final parentDetails = ParentDetails.fromJson(json.decode(existingData));
            final updated = parentDetails.copyWith(phoneNumber: emailOrPhone);
            await prefs.setString('parentDetails', json.encode(updated.toJson()));
          } catch (e) {
            print('Error updating phone number: $e');
          }
        }

        return model;
      }
    } else {
      // Email OTP verification
      final response = await _dio.get(
        '${baseUrlPMSProvider}/authenticate/verifyExistingParentEmailOTP',
        queryParameters: {
          "otp": otp,
          "email": emailOrPhone,
        },
      );

      if (response.data is Map<String, dynamic>) {
        final model = VerifyExistingParentOtpResponseModel.fromJson(response.data);

        // 🔄 Update email in local storage
        final String? existingData = prefs.getString('parentDetails');
        if (existingData != null) {
          try {
            final parentDetails = ParentDetails.fromJson(json.decode(existingData));
            final updated = parentDetails.copyWith(email: emailOrPhone);
            await prefs.setString('parentDetails', json.encode(updated.toJson()));
          } catch (e) {
            print('Error updating email: $e');
          }
        }

        return model;
      }
    }

    throw Exception('Failed to verify OTP');
  }

  Future<void> getAllMasters()async{
      final prefs = await SharedPreferences.getInstance();
      final storedData = prefs.getString('allMasters');

      if (storedData != null && storedData.isNotEmpty) {
        print('Using cached AllMasters data.');
        return;
      }
    try {
      final response = await _dio.get('${baseUrlPMSProvider}/common/getAllMasters');
      if (response.data["SuccessMessage"] == "Sucess") {
          // Decode response into Map
          final decodedData = response.data as Map<String, dynamic>;
          await prefs.setString('allMasters', json.encode(decodedData));

          print('AllMasters saved locally!');
        } else {
          throw Exception('Failed to load data: ${response.statusCode}');
        }

    } catch (e) {
      
    }
  }

  Future<List<EmployerName>> getAllEmployeeNameByTypeId({required int employerTypeId})async{
    try {
      final response = await _dio.get('${baseUrlPMSProvider}/employee/getAllEmployeeNameByTypeId',queryParameters:{"employerTypeId":employerTypeId} );
      print('Status code: ${response.statusCode}');
      if (response.statusCode == 200 && response.data is List) {
         final data = response.data as List;
      return data.map((e) => EmployerName.fromJson(e)).toList();
        } else {
          throw Exception('Failed to load data: ${response.statusCode}');
        }

    } catch (e) {
      rethrow; 
    }
  }
}

