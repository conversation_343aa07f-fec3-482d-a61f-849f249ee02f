import { Body, Controller, Get, Post, Query, Req, UploadedFile, UseInterceptors } from '@nestjs/common';
import { ParentModel } from '../providers/models/parent/parent.model';
import { ParentDocumentDetailsModel } from '../providers/models/parent/parentDocumentDetails.model';
import { ParentProfileModel } from '../providers/models/parent/parentProfile.model';
import { ParentProfileUpdateModel } from '../providers/models/parent/parentProfileUpdate.model';
import { ParentProfileVMModel } from '../providers/models/parent/parentProfileVM.model';
import { ParentProfileWizardUpdateModel } from '../providers/models/parent/parentProfileWizardUpdate.model';
import { ParentService } from '../providers/services/parent.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { ParentProfileWizardUpdateModel_2 } from '../providers/models/parent/parentProfileWizardUpdate_2.model';
@Controller()
export class ParentController {
  constructor(private readonly service: ParentService) {}

  @Post('dashboard/getAppParentDetails')
  public async getAppParentDetails(
    @Body() body: ParentProfileModel,
    @Req() req?: any,
  ): Promise<ParentProfileModel> {
    // -- not used
    let parentId: string | undefined;
      try {
    const pmsProfile = req?.user?.pmsProfile;
    if (pmsProfile) {
      const parsedProfile = JSON.parse(pmsProfile);
      parentId = parsedProfile?.PersonId;
    }
  } catch (error) {
    console.error('Failed to parse pmsProfile:', error);
  }
  body.parentId = Number(parentId);
    const response = await this.service.getAppParentDetails(body);
    return response;
  }

  @Post('parent/createParent')
  public async createParent(@Body() body: ParentModel): Promise<ParentModel> {
    // -- not used
    const response = await this.service.createParent(body);
    return response;
  }

  @Post('parent/updateParent')
  public async updateParent(@Body() body: ParentModel): Promise<ParentModel> {
    // -- not used
    const response = await this.service.updateParent(body);
    return response;
  }

  @Post('parent/updateParentDocumentDetails')
  public async updateParentDocumentDetails(
    @Body() body: ParentDocumentDetailsModel,
  ): Promise<ParentDocumentDetailsModel> {
    // -- not used
    const response = await this.service.updateParentDocumentDetails(body);
    return response;
  }

  @Post('parent/updateParentProfile')
  public async updateParentProfile(
    @Body() body: ParentProfileVMModel,
  ): Promise<ParentProfileVMModel> {
    // -- not used
    const response = await this.service.updateParentProfile(body);
    return response;
  }
  
  @Post('parent/upload/UploadFileToS3')
    @UseInterceptors(FileInterceptor('file'))
    async uploadFileToS3(@UploadedFile() file: Express.Multer.File) {
      return this.service.uploadFileToS3(file);
  }

   @Post('parent/upload/uploadFileToS3WithOcr')
    @UseInterceptors(FileInterceptor('file'))
    async uploadFileToS3WithOcr(@UploadedFile() file: Express.Multer.File) {
      return this.service.uploadFileToS3WithOcr(file);
  }

  @Post('parent/deleteParent')
  public async deleteParent(@Body() body: ParentModel): Promise<ParentModel> {
    // -- not used
    const response = await this.service.deleteParent(body);
    return response;
  }

  @Get('parent/getAllParents')
  public async getAllParents(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
  ): Promise<ParentProfileModel[]> {
    // -- not used
    const response = await this.service.getAllParents(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
    );
    return response;
  }

  @Get('parent/getAllParentDetalsById')
  public async getAllParentDetailsById(
    @Query('fields') fields?: string,
    @Req() req?: any,
  ): Promise<ParentProfileVMModel[]> {
    // -- not used
    let parentId: string | undefined;
      try {
    const pmsProfile = req?.user?.pmsProfile;
    if (pmsProfile) {
      const parsedProfile = JSON.parse(pmsProfile);
      parentId = parsedProfile?.PersonId;
    }
  } catch (error) {
    console.error('Failed to parse pmsProfile:', error);
  }
    const response = await this.service.getAllParentDetailsById(parentId,fields);
    return response;
  }

  @Get('parent/getAllReviewedParentDetalsById')
  public async getAllReviewedParentDetailsById(
    @Req() req?: any
  ): Promise<ParentProfileVMModel[]> {
    // -- not used

      let parentId: number | undefined;
      try {
    const pmsProfile = req?.user?.pmsProfile;
    if (pmsProfile) {
      const parsedProfile = JSON.parse(pmsProfile);
      parentId = parsedProfile?.PersonId;
    }
  } catch (error) {
    console.error('Failed to parse pmsProfile:', error);
  }
    const response =
      await this.service.getAllReviewedParentDetailsById(parentId);
    return response;
  }

  @Get('parent/getAllNotVeryfiedParents')
  public async getAllNotVerifiedParents(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
  ): Promise<ParentProfileVMModel[]> {
    // -- not used
    const response = await this.service.getAllNotVerifiedParents(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
    );
    return response;
  }

  @Get('parent/verifyParentEmiratesId')
  public async verifyParentEmiratesId(
    emiratesNo?: string,
    requestId?: string,
    oTP?: string,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.service.verifyParentEmiratesId(
      emiratesNo,
      requestId,
      oTP,
    );
    return response;
  }

  @Get('parent/checkSpouseEmiratesId')
  public async checkSpouseEmiratesId(
    emiratesNo?: string,
    familyId?: number,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.service.checkSpouseEmiratesId(
      emiratesNo,
      familyId,
    );
    return response;
  }

  @Get('parent/checkParentEmiratesId')
  public async checkParentEmiratesId(
    @Query('emiratesNo') emiratesNo?: string,
    @Req() req?: any
  ): Promise<ParentModel> {
    // -- not used

    let parentId: number | undefined;
      try {
    const pmsProfile = req?.user?.pmsProfile;
    if (pmsProfile) {
      const parsedProfile = JSON.parse(pmsProfile);
      parentId = parsedProfile?.PersonId;
    }
  } catch (error) {
    console.error('Failed to parse pmsProfile:', error);
  }
    const response = await this.service.checkParentEmiratesId(
      emiratesNo,
      parentId,
    );
    return response;
  }

  @Get('parent/checkSpouseEmiratesIdByPass')
  public async checkSpouseEmiratesIdByPass(
    emiratesNo?: string,
    familyId?: number,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.service.checkSpouseEmiratesIdByPass(
      emiratesNo,
      familyId,
    );
    return response;
  }

  @Get('parent/verifyParentEmiratesIdByPass')
  public async verifyParentEmiratesIdByPass(
    emiratesNo?: string,
    requestId?: string,
    oTP?: string,
  ): Promise<ParentModel> {
    // -- not used
    const response = await this.service.verifyParentEmiratesIdByPass(
      emiratesNo,
      requestId,
      oTP,
    );
    return response;
  }

  @Post('parent/updateParentProfileForWizard')
  public async updateParentProfileForWizard(
    @Body() body: ParentProfileWizardUpdateModel,
  ): Promise<ParentProfileWizardUpdateModel> {
    // -- not used
    const response = await this.service.updateParentProfileForWizard(body);
    return response;
  }

  @Post('parent/updateParentProfileForWizard_2')
  public async updateParentProfileForWizard_2(
    @Body() body: ParentProfileWizardUpdateModel_2,
  ): Promise<ParentProfileWizardUpdateModel_2> {
    // -- not used
    const response = await this.service.updateParentProfileForWizard_2(body);
    return response;
  }

  @Post('parent/parentProfileUpdate')
  public async parentProfileUpdate(
    @Body() body: ParentProfileUpdateModel,
  ): Promise<ParentProfileUpdateModel> {
    // -- not used
    const response = await this.service.parentProfileUpdate(body);
    return response;
  }

  @Get('parent/getllParentDetails')
  public async getAllParentDetails(): Promise<ParentProfileVMModel[]> {
    // -- not used
    const response = await this.service.getAllParentDetails();
    return response;
  }

  @Get('parentmenu/getLoggedInParentMenusData')
  public async getLoggedInParentMenusData(
    @Query('familyId') familyId?: number,
    @Query('kidId') kidId?: number,
    @Req() req?: any
  ): Promise<ParentProfileModel[]> {
    // -- not used
     let parentId: number | undefined;
      try {
    const pmsProfile = req?.user?.pmsProfile;
    if (pmsProfile) {
      const parsedProfile = JSON.parse(pmsProfile);
      parentId = parsedProfile?.PersonId;
    }
  } catch (error) {
    console.error('Failed to parse pmsProfile:', error);
  }
    const response = await this.service.getLoggedInParentMenusData(
      parentId,
      familyId,
      kidId,
    );
    return response;
  }
}
