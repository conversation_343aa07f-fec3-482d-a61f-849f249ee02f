import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/screens/account/Document/model/documentTypeModel.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';
import 'package:seawork/screens/employee/claim/loanRequestForm.dart';
import 'package:seawork/screens/employee/claim/reimbursementRequestForm.dart';
import 'package:seawork/screens/employee/letterRequest/requestForm.dart';
import 'package:seawork/screens/parent/ParentSplash/components/getStart.dart';
import 'package:seawork/screens/parent/ParentSplash/components/initialScreen.dart';
import 'package:seawork/screens/parent/ParentSplash/components/parentEmailVerfication.dart';
import 'package:seawork/screens/parent/ParentSplash/components/parentRole.dart';
import 'package:seawork/screens/parent/application/applicationDetailsScreen.dart';
import 'package:seawork/screens/parent/application/applicationSelectionScreen.dart';
import 'package:seawork/screens/parent/childProfile/components/addNewChild.dart';
import 'package:seawork/screens/parent/family/components/addFamily.dart';
import 'package:seawork/screens/public/login/login.dart';
import 'package:seawork/screens/service/ticket/help.dart';
import 'package:seawork/screens/dashboard/dashboard/components/searchwidget.dart';
import 'package:seawork/screens/notification/notification.dart';
import 'package:seawork/screens/parent/childProfile/childDetails.dart';
import 'package:seawork/screens/employee/calender/calenderScreen.dart';

// Add all navigation targets here:
import 'package:seawork/screens/employee/claim/reimbursementRequest.dart';
import 'package:seawork/screens/employee/approval/approval.dart';
import 'package:seawork/screens/employee/claim/educationAllowanceReq.dart';
import 'package:seawork/screens/employee/claim/loanRequestScreen.dart';
import 'package:seawork/screens/employee/claim/claims.dart';
import 'package:seawork/screens/employee/claim/travelClaim.dart';
import 'package:seawork/screens/employee/absence/toilRequest.dart';
import 'package:seawork/screens/employee/letterRequest/letterApproval.dart';
import 'package:seawork/screens/employee/absence/wFHRequest.dart';
import 'package:seawork/screens/employee/absence/applyLeave.dart';
import 'package:seawork/screens/employee/claim/airTicketScreen.dart';
import 'package:seawork/screens/account/profile/peopleDirectory.dart';
import 'package:seawork/screens/employee/letterRequest/noc.dart';
import 'package:seawork/screens/employee/paySlip/salaryInfo.dart';
import 'package:seawork/screens/employee/paySlip/paySlipScreen.dart';
import 'package:seawork/screens/account/profile/profileInfo.dart';
import 'package:seawork/screens/account/profile/myAccount.dart';
import 'package:seawork/screens/account/document/myDocumet.dart';
import 'package:seawork/screens/account/asset/myAssets.dart';
import 'package:seawork/screens/account/profile/privacySettings.dart';
import 'package:seawork/screens/account/profile/personalInfo/personalInfo.dart';
import 'package:seawork/screens/account/profile/contactInfo/contactInfo.dart';
import 'package:seawork/screens/account/employeeInfo.dart';
import 'package:seawork/screens/account/Document/components/documentUploadSheet.dart';
import 'package:seawork/screens/account/profile/myProfile.dart';
import 'package:seawork/screens/employee/claim/appointment.dart';
import 'package:seawork/screens/employee/claim/bussinessTravelClaim.dart';
import 'package:seawork/screens/employee/claim/businessAdvanceTravelClaim.dart';
import 'package:seawork/screens/service/ticket/repository/requestAndReport.dart';
import 'package:seawork/screens/student/student/components/studentDetail.dart';
import 'package:seawork/screens/employee/employee/components/employee.dart';
import 'package:seawork/screens/parent/ParentSplash/components/parentMobileVerification.dart';
import 'package:seawork/screens/dashboard/parentDashboard/profileDashboard.dart';
import 'package:seawork/screens/student/student/components/newRequest.dart';
import 'package:seawork/screens/employee/absence/components/withdrawalreason.dart';
import 'package:seawork/screens/service/ticket/searchTickets.dart';
import 'package:seawork/screens/service/ticket/ticketCategorySelection.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

final GoRouter appRouter = GoRouter(
  navigatorKey: navigatorKey,
  debugLogDiagnostics: true,
   errorBuilder: (context, state) => ErrorScreen(state.error),
  redirect: (BuildContext context, GoRouterState state) {
   
      return null;
  },
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => InitialScreen(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => Login(),
    ),
    GoRoute(
      path: '/oauthredirect',
      builder: (context, state) => Login(),
    ),
    GoRoute(
      path: '/dashboard',
      pageBuilder: (context, state) => CustomTransitionPage(
        key: state.pageKey,
        child: MainDashboard(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;
          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
      ),
    ),
    GoRoute(
      path: '/help',
      builder: (context, state) => Help(iconClicked: 'Help'),
    ),
    GoRoute(
      path: '/reimbursement',
      builder: (context, state) => const ReimbursementScreen(),
    ),
    GoRoute(
      path: '/approvals',
      builder: (context, state) => const Approval(),
    ),
    GoRoute(
      path: '/education-allowance',
      builder: (context, state) => EducationAllowance(),
    ),
    GoRoute(
      path: '/loan-request',
      builder: (context, state) => const ScreenLoan(),
    ),
    GoRoute(
      path: '/claim',
      builder: (context, state) => ClaimScreen(),
    ),
    GoRoute(
      path: '/travel-claims',
      builder: (context, state) => const TravelClaim(),
    ),
    GoRoute(
      path: '/toil-request',
      builder: (context, state) => const ToilRequest(),
    ),
    GoRoute(
      path: '/letter-approval',
      builder: (context, state) => const LetterApprovals(iconClicked: 'Letter request'),
    ),
    GoRoute(
      path: '/wfh-request',
      builder: (context, state) => WFHRequest(request: 'Work from home'),
    ),
    GoRoute(
      path: '/apply-leave',
      builder: (context, state) => ApplyLeave(),
    ),
    GoRoute(
      path: '/air-ticket',
      builder: (context, state) => const AirTicketScreen(),
    ),
    GoRoute(
      path: '/people-directory',
      builder: (context, state) => const PeopleDirectory(),
    ),
    GoRoute(
      path: '/noc',
      builder: (context, state) => const Noobjection(),
    ),
    GoRoute(
      path: '/salary-info',
      builder: (context, state) => SalaryInfoScreen(),
    ),
    GoRoute(
      path: '/pay-slip',
      builder: (context, state) => PaySlipScreen(),
    ),
    GoRoute(
      path: '/profile-info/:personId',
      builder: (context, state) => ProfileInfo(
        personId: state.pathParameters['personId'] ?? '',
      ),
    ),
    GoRoute(
      path: '/my-account',
      builder: (context, state) => MyAccountScreen(),
    ),
    GoRoute(
      path: '/my-documents',
      builder: (context, state) => MyDocument(),
    ),
    GoRoute(
      path: '/my-assets',
      builder: (context, state) => MyAssetScreen(),
    ),
    GoRoute(
      path: '/privacy-settings',
      builder: (context, state) => PrivacySettingsScreen(),
    ),
    GoRoute(
      path: '/personal-info',
      builder: (context, state) => Personalinfo(),
    ),
    GoRoute(
      path: '/contact-info',
      builder: (context, state) => ContactInfo(),
    ),
    GoRoute(
      path: '/employment-info',
      builder: (context, state) => EmployeeInfo(),
    ),
    GoRoute(
      path: '/my-profile',
      pageBuilder: (context, state) => CustomTransitionPage(
        key: state.pageKey,
        child: ProfileScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;
          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
      ),
    ),
    GoRoute(
      path: '/upload-document',
      builder: (context, state) => UploadDocument(selectedDocumentType: state.extra as MyDocumentTypeModel?),
    ),
    GoRoute(
      path: '/business-advance-travel',
      builder: (context, state) => BuisnessAdvanceTravelRequest(),
    ),
    GoRoute(
      path: '/business-travel-expense',
      builder: (context, state) => BuisnessTravelExpenseRequest(),
    ),
    GoRoute(
      path: '/appointment-travel',
      builder: (context, state) => AppoinmentTravel(),
    ),
    GoRoute(
      path: '/student-list',
      builder: (context, state) => StudentListScreen(),
    ),
    GoRoute(
      path: '/employee-management',
      builder: (context, state) => EmployeeManagementScreen(),
    ),
    GoRoute(
      path: '/search',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return SearchWidgetScreen(
          onItemSelected: extra?['onItemSelected'],
          addRecentlyUsed: extra?['addRecentlyUsed'],
        );
      },
    ),
    GoRoute(
      path: '/notifications',
      builder: (context, state) => const NotificationsScreen(),
    ),
    GoRoute(
      path: '/parent-mobile-verification',
      builder: (context, state) => const ParentMobileVerificationScreen(),
    ),
    GoRoute(
      path: '/profile-dashboard',
      builder: (context, state) => ProfileDashboard(),
    ),
    GoRoute(
      path: '/withdrawal-reasons',
      builder: (context, state) => WithdrawalReasonsScreen(),
    ),
    GoRoute(
      path: '/new-request',
      builder: (context, state) => NewRequestScreen(),
    ),
    GoRoute(
      path: '/search-tickets',
      builder: (context, state) => const SearchTickets(),
    ),
    GoRoute(
      path: '/ticket-category-selection',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return TicketCategorySelection(
          key: const ValueKey(2),
          iconClicked: extra?['iconClicked'],
          itilTicketTypeId: extra?['itilTicketTypeId'],
        );
      },
    ),
    GoRoute(
      path: '/request-detail',
      builder: (context, state) {
        final args = state.extra as Map<String, dynamic>?;
        final letterRequestType = args?['letterRequestType'];
        final originContext = args?['originContext'];
        return RequestDetail(
          letterRequestType: letterRequestType,
          originContext: originContext,
        );
      },
    ),
    GoRoute(
      path: '/parent-email-verification',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return ParentEmailVerificationScreen(
          forRegistration: extra?['forRegistration'] ?? false,
        );
      },
    ),
    GoRoute(
      path: '/get-start',
      builder: (context, state) => GetStart(),
    ),
    GoRoute(
      path: '/role-selection',
      builder: (context, state) => RoleSelectionScreen(),
    ),
    GoRoute(
      path: '/add-family',
      builder: (context, state) => AddFamilyScreen(),
    ),
    GoRoute(
      path: '/add-new-child',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return AddNewChildScreen(childId: extra?['childId']);
      },
    ),
    GoRoute(
      path: '/application-details',
      builder: (context, state) => const ApplicationDetailsScreen(),
    ),
    GoRoute(
      path: '/application-selection',
      builder: (context, state) => const ApplicationSelectionScreen(),
    ),
    GoRoute(
      path: '/reimbursement-advance-form',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return ReimbursementAdvanceForm(
          reimbursementTypes: extra?['reimbursementTypes'],
        );
      },
    ),
    GoRoute(
      path: '/loan-advance-form',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return LoanAdvanceForm(loanType: extra?['loanType']);
      },
    ),
    GoRoute(
      path: '/request-and-report',
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;
        return RequestAndReport(
          itilTicketTypeId: extra?['itilTicketTypeId'] ?? 0,
          serviceCategoryId: extra?['serviceCategoryId'] ?? 0,
          iconClicked: extra?['iconClicked'] ?? '',
          requestType: extra?['requestType'] ?? '',
          prefilledDescription: extra?['prefilledDescription'] ?? '',
        );
      },
    ),
    GoRoute(
      path: '/child-details',
      builder: (context, state) => ChildDetailsScreen(),
    ),
    GoRoute(
      path: '/calendar',
      pageBuilder: (context, state) => CustomTransitionPage(
        key: state.pageKey,
        child: CalendarScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;
          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
      ),
    ),
    // Add more routes as needed
  ],
);

class ErrorScreen extends StatelessWidget {
  final Object? error;

  const ErrorScreen(this.error, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Text(
          'An error occurred: $error',
          style: const TextStyle(fontSize: 18, color: Colors.red),
        ),
      ),
    );
  }
}