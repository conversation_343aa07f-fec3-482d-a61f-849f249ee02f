import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:seawork/screens/employee/paySlip/models/payslipModel.dart';
import 'package:universal_html/html.dart' as web;

import 'package:seawork/data/module/networkModule.dart';

class PayslipRepository {
    final Dio _dio;

  PayslipRepository(this._dio);

  Future<List<PaySlip>> fetchPayslips({
    int limit = 25,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/payslips',
        queryParameters: {
          'fields':
              'DocumentsOfRecordId,Amount,DefaultCurrencyCode,PaymentDate,RelActionId,CreationDate,CurrencyCode,WeekDay,PeriodEndDate,PeriodStartDate',
          'orderBy': 'CreationDate:desc',
          'totalResults': 'true',
          'limit': limit,
          'offset': offset,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> items = data['items'];
        return items.map((item) => PaySlip.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load payslips: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching payslips: $e');
      }
      throw Exception('Failed to load payslips: $e');
    }
  }

  Future<String> downloadPayslipAttachment(int documentsOfRecordId) async {
    try {
      // Step 1: Get document details
      final documentResponse = await _dio.get(
        '${baseUrlEMSProvider}/payslips/$documentsOfRecordId',
        queryParameters: {
          // 'fields': 'documents',
          // 'onlyData': 'true',
          'expand': 'documents',
        },
      );

      // Extract documentsUniqID
      final documents = documentResponse.data['documents'] as List;
      if (documents.isEmpty) throw Exception('No documents found');

      final selfLink = (documents.first['links'] as List)
          .firstWhere((link) => link['rel'] == 'self')['href'] as String;
      final documentsUniqID = selfLink.split('/').last;

      // Step 2: Download PDF content
      final pdfResponse = await _dio.get(
        '${baseUrlEMSProvider}/payslips/$documentsOfRecordId/child/documents/$documentsUniqID/enclosure/fileContents',
        options: Options(
          responseType: ResponseType.bytes,
          headers: {'Accept': 'application/pdf'},
        ),
      );

      if (kIsWeb) {
        final bytes = pdfResponse.data as List<int>;

        final blob =
            web.Blob([Uint8List.fromList(bytes)], 'application/octet-stream');
        final url = web.Url.createObjectUrlFromBlob(blob);

        final anchor = web.AnchorElement()
          ..href = url
          ..download = 'payslip_$documentsOfRecordId.pdf'
          ..style.display = 'none'
          ..setAttribute('type', 'application/octet-stream');

        web.document.body!.append(anchor);
        anchor.click();

        Future.delayed(Duration(seconds: 2), () {
          anchor.remove();
          web.Url.revokeObjectUrl(url);
        });

        return '';
      } else {
        // Mobile file handling
        final directory = await getTemporaryDirectory();
        final filePath = '${directory.path}/payslip_$documentsOfRecordId.pdf';
        final file = File(filePath);
        await file.writeAsBytes(pdfResponse.data);
        return filePath;
      }
    } catch (e) {
      if (kDebugMode) print('Error downloading payslip: $e');
      throw Exception('Failed to download payslip: $e');
    }
  }
}
