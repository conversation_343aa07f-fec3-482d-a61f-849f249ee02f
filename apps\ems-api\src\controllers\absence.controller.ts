import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  // UploadedFile,
  // UsePipes,
  // ValidationPipe,
  // BadRequestException,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { AbsencesSearchDto } from '../dto/absences-search.dto';
import { ValueTransformPipe } from '../pipes/valuetransform.pipe';
import { AbsenceBusinessTitlesLOVModel } from '../providers/models/absence/absenceBusinessTitlesLOV.model';
import { AbsenceBusinessTitlesLOVItemResponseModel } from '../providers/models/absence/absenceBusinessTitlesLOVItemResponse.model';
import { AbsencePlansLOVModel } from '../providers/models/absence/absencePlansLOV.model';
import { AbsencePlansLOVItemResponseModel } from '../providers/models/absence/absencePlansLOVItemResponse.model';
import { AbsencesModel } from '../providers/models/absence/absences.model';
import { AbsencesAbsenceAttachmentsItemPostRequestModel } from '../providers/models/absence/absencesAbsenceAttachmentsItemPostRequest.model';
import { AbsencesAbsenceAttachmentsItemResponseModel } from '../providers/models/absence/absencesAbsenceAttachmentsItemResponse.model';
import { AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel } from '../providers/models/absence/absencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponse.model';
import { AbsencesAbsenceEntitlementsItemResponseModel } from '../providers/models/absence/absencesAbsenceEntitlementsItemResponse.model';
import { AbsencesAbsenceEntryDetailsItemPostRequestModel } from '../providers/models/absence/absencesAbsenceEntryDetailsItemPostRequest.model';
import { AbsencesAbsenceEntryDetailsItemResponseModel } from '../providers/models/absence/absencesAbsenceEntryDetailsItemResponse.model';
import { AbsencesAbsenceMaternityItemPostRequestModel } from '../providers/models/absence/absencesAbsenceMaternityItemPostRequest.model';
import { AbsencesAbsenceMaternityItemResponseModel } from '../providers/models/absence/absencesAbsenceMaternityItemResponse.model';
import { AbsencesAbsenceRecordingDFFItemPostRequestModel } from '../providers/models/absence/absencesAbsenceRecordingDFFItemPostRequest.model';
import { AbsencesAbsenceRecordingDFFItemResponseModel } from '../providers/models/absence/absencesAbsenceRecordingDFFItemResponse.model';
import { AbsencesAbsenceRecordingsDDFItemPostRequestModel } from '../providers/models/absence/absencesAbsenceRecordingsDDFItemPostRequest.model';
import { AbsencesAbsenceRecordingsDDFItemResponseModel } from '../providers/models/absence/absencesAbsenceRecordingsDDFItemResponse.model';
import { AbsencesItemPostRequestModel } from '../providers/models/absence/absencesItemPostRequest.model';
import { AbsencesItemResponseModel } from '../providers/models/absence/absencesItemResponse.model';
import { AbsenceTypeReasonsLOVModel } from '../providers/models/absence/absenceTypeReasonsLOV.model';
import { AbsenceTypeReasonsLOVItemResponseModel } from '../providers/models/absence/absenceTypeReasonsLOVItemResponse.model';
import { AbsenceTypesLOVModel } from '../providers/models/absence/absenceTypesLOV.model';
import { AbsenceTypesLOVItemResponseModel } from '../providers/models/absence/absenceTypesLOVItemResponse.model';
import { PlanBalanceResponseModel } from '../providers/models/absence/planBalances.model';
import { PlanBalanceSummaryItemModel } from '../providers/models/absence/planBalanceSummary.model';
import { AbsenceService } from '../providers/services/absence.service';
@Controller()
export class AbsenceController {
  constructor(private readonly service: AbsenceService) {}

  @Get('absenceTypeReasonsLOV')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AbsenceTypeReasonsLOVModel> })
  public async getAbsenceTypeReasons(
    @Query() query?: any,
  ): Promise<AbsenceTypeReasonsLOVModel[]> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    } = query;
    const response = await this.service.getAbsenceTypeReasons(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    );
    return response;
  }

  @Get('absenceTypeReasonsLOV/:absenceTypeReasonsLOVUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsenceTypeReasonsLOVItemResponseModel })
  public async getAbsenceTypeReasonsLOV(
    @Query() query?: any,
  ): Promise<AbsenceTypeReasonsLOVItemResponseModel> {
    // -- not used
    const {
      absenceTypeReasonsLOVUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    } = query;
    const response = await this.service.getAbsenceTypeReasonsLOV(
      absenceTypeReasonsLOVUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }

  @Get('absenceBusinessTitlesLOV/:assignmentPk')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AbsenceBusinessTitlesLOVItemResponseModel> })
  public async getAbsenceBusinessTitleById(
    @Query() query?: any,
  ): Promise<AbsenceBusinessTitlesLOVItemResponseModel[]> {
    // -- not used
    const { assignmentPk, expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getAbsenceBusinessTitleById(
      assignmentPk,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('absenceBusinessTitlesLOV')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AbsenceBusinessTitlesLOVModel> })
  public async getAbsenceBusinessTitles(
    @Query() query?: any,
  ): Promise<AbsenceBusinessTitlesLOVModel[]> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    } = query;
    const response = await this.service.getAbsenceBusinessTitles(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );
    return response;
  }

  @Get('absences')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AbsencesModel> })
  public async getAbsences_2(@Query() query?: any,    @Req() req?: any,
): Promise<AbsencesModel[]> {
    // -- not used
    let {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    } = query;
    q=`PersonId=${req?.user?.fusionProfile?.PersonId};`+q;
    const response = await this.service.getAbsences_2(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );
    return response;
  }

  @Get('planBalances')
  @ApiResponse({ type: [PlanBalanceResponseModel] })
  public async getPlanBalances(
    @Query() query?: any,
        @Req() req?: any,

  ): Promise<PlanBalanceResponseModel[]> {
    let q = 'personId='+req?.user?.fusionProfile?.PersonId;
    const response = await this.service.getPlanBalances(q);
    return response;
  }

  @Get('planBalances/:personPlanEnrollmentId/child/planBalanceSummary')
  @ApiResponse({ type: [PlanBalanceSummaryItemModel] })
  public async getPlanBalanceSummary(
    @Param('personPlanEnrollmentId') personPlanEnrollmentId: string,
  ): Promise<PlanBalanceSummaryItemModel[]> {
    const response = await this.service.getPlanBalanceSummary(
      personPlanEnrollmentId,
    );
    return response;
  }

  @Post('absences')
  @ApiBody({ type: AbsencesItemPostRequestModel })
  @ApiResponse({ type: AbsencesItemPostRequestModel })
  public async postAbsences_2(
    @Body() body: AbsencesItemPostRequestModel,
  ): Promise<AbsencesItemPostRequestModel> {
    // -- not used
    const response = await this.service.postAbsences_2(body);
    return response;
  }

  @Patch('absences/:absencesUniqID')
  @ApiBody({ type: AbsencesItemPostRequestModel })
  @ApiResponse({ type: AbsencesItemPostRequestModel })
  public async updateAbsence(
    @Param('absencesUniqID') absencesUniqID: string,
    @Body() body: AbsencesItemPostRequestModel,
  ): Promise<AbsencesItemPostRequestModel> {
    if (!absencesUniqID) {
      throw new BadRequestException('absencesUniqID is required');
    }

    const response = await this.service.patchAbsences_2(absencesUniqID, body);
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceEntitlements/:absenceEntitlementsUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceEntitlementsItemResponseModel })
  public async getAbsenceEntitlements(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceEntitlementsItemResponseModel> {
    // -- not used
    const {
      absencesUniqID,
      absenceEntitlementsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAbsenceEntitlements(
      absencesUniqID,
      absenceEntitlementsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceEntitlements/:absenceEntitlementsUniqID/child/absenceEntitlementDetails/:entitlementDetailId',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({
    type: AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel,
  })
  public async getAbsenceEntitlementDetails(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel> {
    // -- not used
    const {
      absencesUniqID,
      absenceEntitlementsUniqID,
      entitlementDetailId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAbsenceEntitlementDetails(
      absencesUniqID,
      absenceEntitlementsUniqID,
      entitlementDetailId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('absences/:absencesUniqID/child/absenceRecordingsDDF')
  @ApiBody({ type: AbsencesAbsenceRecordingsDDFItemPostRequestModel })
  @ApiResponse({ type: AbsencesAbsenceRecordingsDDFItemPostRequestModel })
  public async postAbsenceRecordingsDDF(
    @Body() body: AbsencesAbsenceRecordingsDDFItemPostRequestModel,
  ): Promise<AbsencesAbsenceRecordingsDDFItemPostRequestModel> {
    // -- not used
    const response = await this.service.postAbsenceRecordingsDDF(body);
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceAttachments/:absenceAttachmentsUniqID',
  )
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceAttachmentsItemResponseModel })
  public async getAbsenceAttachments(
    @Param('absencesUniqID') absencesUniqID?: string,
    @Param('absenceAttachmentsUniqID') absenceAttachmentsUniqID?: string,
    @Query() query?: any,
  ): Promise<AbsencesAbsenceAttachmentsItemResponseModel> {
    // -- not used
    const { expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getAbsenceAttachments(
      absencesUniqID,
      absenceAttachmentsUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Delete(
    'absences/:absencesUniqID/child/absenceAttachments/:absenceAttachmentsUniqID',
  )
  public async deleteAbsenceAttachments(
    @Param('absencesUniqID') absencesUniqID: string,
    @Param('absenceAttachmentsUniqID') absenceAttachmentsUniqID: string,
  ): Promise<any> {
    // -- not used
    const response = await this.service.deleteAbsenceAttachments(
      absencesUniqID,
      absenceAttachmentsUniqID,
    );
    return response;
  }

  @Get('absences/:absencesUniqID')
  // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesItemResponseModel })
  public async getAbsences(
    @Param('absencesUniqID') absencesUniqID: string,

    @Query() query?: any,
  ): Promise<AbsencesItemResponseModel> {
    // -- not used
    const { expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getAbsences(
      absencesUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('absences/action/loadProjectedBalance')
  @ApiBody({ type: AbsencesItemResponseModel })
  @ApiResponse({ type: AbsencesItemResponseModel })
  public async loadProjectedBalance(
    @Body() body: AbsencesItemResponseModel,
  ): Promise<AbsencesItemResponseModel> {
    // -- not used
    const response = await this.service.loadProjectedBalance(body);
    return response;
  }

  @Post('absences/:absencesUniqID/child/absenceAttachments')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({ type: AbsencesAbsenceAttachmentsItemPostRequestModel })
  @ApiResponse({ type: AbsencesAbsenceAttachmentsItemPostRequestModel })
  public async postAbsenceAttachments(
    @Body() body: AbsencesAbsenceAttachmentsItemPostRequestModel,
    @UploadedFile() file?: any,
    @Param('absencesUniqID') absencesUniqID?: string,
  ): Promise<AbsencesAbsenceAttachmentsItemPostRequestModel> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Convert uploaded file to base64
    const fileContents = file.buffer.toString('base64');

    // Map file metadata to request body
    body.FileName = file.originalname;
    body.FileContents = fileContents;
    body.DatatypeCode = 'FILE';

    // Optional fields
    body.CategoryName = body.CategoryName || 'General'; // Default category
    body.Description = body.Description || 'Uploaded document'; // Default description

    const response = await this.service.postAbsenceAttachments(
      body,
      absencesUniqID,
    );
    return response;
  }

  // @Post('absences/:absencesUniqID/child/absenceAttachments')
  // @UseInterceptors(FileInterceptor('file'))
  // @ApiBody({ type: AbsencesAbsenceAttachmentsItemPostRequestModel })
  // @ApiResponse({ type: AbsencesAbsenceAttachmentsItemPostRequestModel })
  // public async postAbsenceAttachments(
  //   @Body() body: AbsencesAbsenceAttachmentsItemPostRequestModel,
  //   @UploadedFile() file?: Express.Multer.File,
  //       @Param('absencesUniqID') absencesUniqID?: string,
  // ): Promise<AbsencesAbsenceAttachmentsItemPostRequestModel> {
  //   if (!file) {
  //     throw new BadRequestException('No file uploaded');
  //   }

  //   // Convert uploaded file to base64
  //   const fileContents = file.buffer.toString('base64');

  //   // Map file metadata to request body
  //   body.FileName = file.originalname;
  //   body.FileContents = fileContents;
  //   body.DatatypeCode = 'FILE';

  //   // Optional fields
  //   body.CategoryName = body.CategoryName || 'General'; // Default category
  //   body.Description = body.Description || 'Uploaded document'; // Default description

  //   let response = await this.service.postAbsenceAttachments(
  //     body,
  //     absencesUniqID,
  //   );
  //   return response;
  // }
  @Post('absences/:absencesUniqID/child/absenceMaternity')
  @ApiBody({ type: AbsencesAbsenceMaternityItemPostRequestModel })
  @ApiResponse({ type: AbsencesAbsenceMaternityItemPostRequestModel })
  public async postAbsenceMaternity(
    @Body() body: AbsencesAbsenceMaternityItemPostRequestModel,
  ): Promise<AbsencesAbsenceMaternityItemPostRequestModel> {
    // -- not used
    const response = await this.service.postAbsenceMaternity(body);
    return response;
  }

  @Post('absences/action/getAbsenceTypeBalance')
  @ApiBody({
    type: AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel,
  })
  @ApiResponse({
    type: AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel,
  })
  public async postAbsenceTypeBalance(
    @Body()
    body: AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel,
  ): Promise<AbsencesAbsenceEntitlementsAbsenceEntitlementDetailsItemResponseModel> {
    // -- not used
    const response = await this.service.postAbsenceTypeBalance(body);
    return response;
  }

  @Post('absences/:absencesUniqID/child/absenceEntryDetails')
  @ApiBody({ type: AbsencesAbsenceEntryDetailsItemPostRequestModel })
  @ApiResponse({ type: AbsencesAbsenceEntryDetailsItemPostRequestModel })
  public async postAbsenceEntryDetails(
    @Body() body: AbsencesAbsenceEntryDetailsItemPostRequestModel,
  ): Promise<AbsencesAbsenceEntryDetailsItemPostRequestModel> {
    // -- not used
    const response = await this.service.postAbsenceEntryDetails(body);
    return response;
  }

  @Post('absences/action/findByAdvancedSearchQuery')
  @ApiBody({ type: AbsencesSearchDto })
  @ApiResponse({ type: AbsencesItemResponseModel })
  public async findByAdvancedSearchQuery(
    @Body() body: AbsencesSearchDto,
  ): Promise<any> {
    console.log(body);
    // -- not used
    const response = await this.service.findByAdvancedSearchQuery(body);
    return response;
  }

  @Post('absences/:absencesUniqID/child/absenceRecordingDFF')
  @ApiBody({ type: AbsencesAbsenceRecordingDFFItemPostRequestModel })
  @ApiResponse({ type: AbsencesAbsenceRecordingDFFItemPostRequestModel })
  public async postAbsenceRecordingDFF(
    @Body() body: AbsencesAbsenceRecordingDFFItemPostRequestModel,
  ): Promise<AbsencesAbsenceRecordingDFFItemPostRequestModel> {
    // -- not used
    const response = await this.service.postAbsenceRecordingDFF(body);
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceEntryDetails/:personAbsenceEntryDetailId',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceEntryDetailsItemResponseModel })
  public async getAbsenceEntryDetails(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceEntryDetailsItemResponseModel> {
    // -- not used
    const {
      absencesUniqID,
      personAbsenceEntryDetailId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAbsenceEntryDetails(
      absencesUniqID,
      personAbsenceEntryDetailId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceAttachments/:absenceAttachmentsUniqID/enclosure/fileWebImage',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceAttachmentsItemResponseModel })
  public async getAbsenceAttachmentsFileWebImage(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceAttachmentsItemResponseModel> {
    // -- not used
    const { absencesUniqID, absenceAttachmentsUniqID } = query;
    const response = await this.service.getAbsenceAttachmentsFileWebImage(
      absencesUniqID,
      absenceAttachmentsUniqID,
    );
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceAttachments/:absenceAttachmentsUniqID/enclosure/fileContents',
  )
  @ApiResponse({ type: AbsencesAbsenceAttachmentsItemResponseModel })
  public async getAbsenceAttachmentsFileContents(
    @Param('absencesUniqID') absencesUniqID: string,
    @Param('absenceAttachmentsUniqID') absenceAttachmentsUniqID: string,
    @Query() query?: any,
    @Res() res?: any,
  ): Promise<any> {
    const { contentType, fileName } = query;
    const response = await this.service.getAbsenceAttachmentsFileContents(
      absencesUniqID,
      absenceAttachmentsUniqID,
    );
    const rawPdfContent: any = response;
    console.log(rawPdfContent.slice(0, 10));
    const buffer = Buffer.from(rawPdfContent);

    // Set headers to trigger download
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="' + fileName + '"',
    );
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', buffer.length);

    // Send the buffer directly in the response
    res.end(buffer);
  }

  // @Get(
  //   'absences/:absencesUniqID/child/absenceAttachments/:absenceAttachmentsUniqID/enclosure/fileContents',
  // )
  // // @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  // @ApiResponse({ type: AbsencesAbsenceAttachmentsItemResponseModel })
  // public async getAbsenceAttachmentsFileContents(
  //   @Query() query?: any,
  //   @Param('absencesUniqID') absencesUniqID?: any,
  //   @Param('absenceAttachmentsUniqID') absenceAttachmentsUniqID?: any,
  // ): Promise<any> {
  //   // -- not used
  //   // let { absencesUniqID, absenceAttachmentsUniqID } = query;
  //   let response = await this.service.getAbsenceAttachmentsFileContents(
  //     absencesUniqID,
  //     absenceAttachmentsUniqID,
  //   );
  //   return response;
  // }

  @Post(
    'absences/:absencesUniqID/child/absenceAttachments/:absenceAttachmentsUniqID/enclosure/fileContents',
  )
  @ApiBody({ type: AbsencesAbsenceAttachmentsItemResponseModel })
  @ApiResponse({ type: AbsencesAbsenceAttachmentsItemResponseModel })
  public async putAbsenceAttachmentsFileContents(
    @Body() body: AbsencesAbsenceAttachmentsItemResponseModel,
  ): Promise<AbsencesAbsenceAttachmentsItemResponseModel> {
    // -- not used
    const response = await this.service.putAbsenceAttachmentsFileContents(body);
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceRecordingsDDF/:absenceRecordingsDDFUniqID',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceRecordingsDDFItemResponseModel })
  public async getAbsenceRecordingsDDF(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceRecordingsDDFItemResponseModel> {
    // -- not used
    const {
      absencesUniqID,
      absenceRecordingsDDFUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAbsenceRecordingsDDF(
      absencesUniqID,
      absenceRecordingsDDFUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('absences/:absencesUniqID/child/absenceRecordingDFF/:perAbsenceEntryId')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceRecordingDFFItemResponseModel })
  public async getAbsenceRecordingDFF(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceRecordingDFFItemResponseModel> {
    // -- not used
    const {
      absencesUniqID,
      perAbsenceEntryId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAbsenceRecordingDFF(
      absencesUniqID,
      perAbsenceEntryId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Post('absences/action/absenceDailyDetailsBreakdown')
  @ApiBody({ type: AbsencesItemResponseModel })
  @ApiResponse({ type: AbsencesItemResponseModel })
  public async absenceDailyDetailsBreakdown(
    @Body() body: AbsencesItemResponseModel,
  ): Promise<AbsencesItemResponseModel> {
    // -- not used
    const response = await this.service.absenceDailyDetailsBreakdown(body);
    return response;
  }

  @Post('absences/action/findByAdvancedSearchQueryForCases')
  @ApiBody({ type: AbsencesItemResponseModel })
  @ApiResponse({ type: AbsencesItemResponseModel })
  public async findByAdvancedSearchQueryForCases(
    @Body() body: AbsencesItemResponseModel,
  ): Promise<AbsencesItemResponseModel> {
    // -- not used
    const response = await this.service.findByAdvancedSearchQueryForCases(body);
    return response;
  }

  @Get(
    'absences/:absencesUniqID/child/absenceMaternity/:personAbsenceMaternityId',
  )
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencesAbsenceMaternityItemResponseModel })
  public async getAbsenceMaternity(
    @Query() query?: any,
  ): Promise<AbsencesAbsenceMaternityItemResponseModel> {
    // -- not used
    const {
      absencesUniqID,
      personAbsenceMaternityId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    } = query;
    const response = await this.service.getAbsenceMaternity(
      absencesUniqID,
      personAbsenceMaternityId,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('absenceTypesLOV/:compositeKey')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsenceTypesLOVItemResponseModel })
  public async getAbsenceTypeByCompositeKey(
    @Query() query?: any,
  ): Promise<AbsenceTypesLOVItemResponseModel> {
    // -- not used
    const { compositeKey, expand, fields, onlyData, dependency, links } = query;
    const response = await this.service.getAbsenceTypeByCompositeKey(
      compositeKey,
      expand,
      fields,
      onlyData,
      dependency,
      links,
    );
    return response;
  }

  @Get('absenceTypesLOV')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AbsenceTypesLOVModel> })
  public async getAbsenceTypes(
    @Query() query?: any,
    @Req() req?: any,
  ): Promise<AbsenceTypesLOVModel[]> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
    } = query;
    let { finder } = query;
    finder = `findByWord;PersonId=${req?.user?.fusionProfile?.PersonId}`;
    const response = await this.service.getAbsenceTypes(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
    );
    return response;
  }

  @Get('absencePlansLOV/:absencePlansLOVUniqID')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: AbsencePlansLOVItemResponseModel })
  public async getAbsencePlan(
    @Query() query?: any,
  ): Promise<AbsencePlansLOVItemResponseModel> {
    // -- not used
    const {
      absencePlansLOVUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    } = query;
    const response = await this.service.getAbsencePlan(
      absencePlansLOVUniqID,
      expand,
      fields,
      onlyData,
      dependency,
      links,
      effectiveOf,
    );
    return response;
  }

  @Get('absencePlansLOV')
  @UsePipes(ValueTransformPipe, new ValidationPipe({ transform: true }))
  @ApiResponse({ type: Array<AbsencePlansLOVModel> })
  public async getAbsencePlans(
    @Query() query?: any,
  ): Promise<AbsencePlansLOVModel[]> {
    // -- not used
    const {
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    } = query;
    const response = await this.service.getAbsencePlans(
      expand,
      fields,
      onlyData,
      links,
      limit,
      offset,
      totalResults,
      q,
      orderBy,
      finder,
      effectiveDate,
      effectiveOf,
    );
    return response;
  }
}
