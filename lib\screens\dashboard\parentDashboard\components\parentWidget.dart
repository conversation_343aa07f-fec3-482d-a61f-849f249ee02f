import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/ProfileSetup/profileSetup.dart';
import 'package:seawork/screens/dashboard/providers/parent_dashboardProvider.dart';
import 'package:seawork/screens/parent/application/applicationScreen.dart';
import 'package:seawork/screens/parent/childProfile/childDetails.dart';
import 'package:seawork/screens/parent/family/myFamily.dart';
import 'package:seawork/screens/parent/family/provider/familyRepositoryProvider.dart';
import 'package:seawork/screens/parent/parent/constants/parentConstants.dart';
import 'package:seawork/screens/parent/parent/providers/parentWorkInfo_provider.dart';
import 'package:seawork/screens/parent/parent/repository/parentRepositoryProvider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/dashboard/parentDashboard/components/progressBottomsheet.dart';

class ParentWidget extends ConsumerStatefulWidget {
  const ParentWidget({super.key});

  @override
  ConsumerState<ParentWidget> createState() => _ParentWidgetState();
}

class _ParentWidgetState extends ConsumerState<ParentWidget> {
  bool _isExpanded = true;

  // Add percentage completion for each section
  final Map<String, int> _completionPercentages = {
    "Application": 40,
    "My Family": 60,
  };

  @override
void initState() {
  super.initState();

  Future.microtask(() async {
    // final parentId = await getStringAsync(PreferencesUtils.PARENT_ID);
    // final parentIdtoInt = int.parse(parentId);
    final parentIdString = await PreferencesUtils.getString(PreferencesUtils.PARENT_ID);
    print('parentId issss $parentIdString');
    final int parentId = int.tryParse(parentIdString ?? '') ?? 0;
    final result = await ref.read(getParentDetailsByIdProvider((parentId: parentId)).future);
    ref.read(currentParentDetailsProvider.notifier).state = result;
    final appParentDetails = await ref.read(postAppParentDetailsProvider((parentId: parentId)).future);
    ref.read(appParentDetailsProvider.notifier).state = appParentDetails;
    final familyResult = await ref.read(getFamilyDetailsByParentSpouseIdProvider((parentId: parentId)).future,);
    ref.read(familyDetailsStateProvider.notifier).state = familyResult;
    final loggedInParentMenusData = await ref.read(getLoggedInParentMenusDataProvider((parentId: parentId,familyId: null,kidId: null)).future);
    ref.read(loggedInParentMenusDataProvider.notifier).state = loggedInParentMenusData;
  });
}

  @override
  Widget build(BuildContext context) {

    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    // Define size ratios
    final double containerMarginRatio = 0.01;
    final double borderRadiusRatio = 0.02;
    final double shadowBlurRatio = 0.02;
    final double paddingHorizontalRatio = 0.05;
    final double paddingVerticalRatio = 0.03;
    final double titleFontSizeRatio = 0.04;
    final double gridMaxHeightRatio = _isExpanded ? 0.45 : 0.45;

    // Calculate overall percentage
    int totalPercentage = 0;
    _completionPercentages.forEach((key, value) {
      totalPercentage += value;
    });
    int overallPercentage = _completionPercentages.isEmpty
        ? 0
        : totalPercentage ~/ _completionPercentages.length;

    return Center(
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 600 : double.infinity,
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(screenWidth * borderRadiusRatio),
          boxShadow: [
            BoxShadow(
              color: AppColors.boxshadowcolor.withOpacity(0.25),
              blurRadius: screenWidth * shadowBlurRatio,
            )
          ],
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 20, right: 20, top: 12, bottom: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    DmSansText(
                      'Parent',
                      color: AppColors.headingColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    Row(
                      children: [
                        // Add percentage indicator circle
                        _buildPercentageCircle(overallPercentage),
                        SizedBox(width: 8),
                        Transform.rotate(
                          angle: _isExpanded ? 0 : 3.14159,
                          child: Icon(
                            Icons.keyboard_arrow_up,
                            size: 34,
                            color: AppColors.viewColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(bottom: screenHeight * 0.015),
                child: Divider(),
              ),
              LimitedBox(
                maxHeight: screenHeight * gridMaxHeightRatio,
                child: _buildEmployeeGrid(context),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPercentageCircle(int percentage) {
    return Container(
      width: 29,
      height: 29,
      child: Stack(
        children: [
          SizedBox(
            width: 29,
            height: 29,
            child: CircularProgressIndicator(
              value: percentage / 100,
              backgroundColor: AppColors.calanderbordercolor2,
              valueColor: AlwaysStoppedAnimation<Color>(
                  percentage == 100 ? Colors.green : Colors.red),
              strokeWidth: 3,
            ),
          ),
          Center(
            child: OpenSansText(
              '$percentage%',
              fontSize: 8,
              fontWeight: FontWeight.bold,
              color: AppColors.blackColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeGrid(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final isSmallScreen = screenWidth <= 320;

    List<Map<String, String>> icons = [
      {"path": "assets/images/parentapplication.svg", "label": "Application"},
      {"path": "assets/images/parentmyfamily.svg", "label": "My Family"},
    ];

    int columns = isSmallScreen ? 3 : _getOptimalColumnCount(screenWidth);
    double spacingRatio = isSmallScreen ? 0.015 : 0.015;
    double aspectRatio = isSmallScreen ? 0.95 : 0.95;

    return GridView.builder(
      physics: const ClampingScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: aspectRatio,
        crossAxisSpacing: screenWidth * spacingRatio,
        mainAxisSpacing: screenHeight * spacingRatio,
      ),
      itemCount: icons.length,
      itemBuilder: (context, index) {
        String label = icons[index]["label"]!;
        int percentage = _completionPercentages[label] ?? 100;

        return GestureDetector(
          onTap: () => _navigateToScreen(label, context),
          child: _buildCompactIconWithLabel(
            icons[index]["path"]!,
            label,
            null,
            context,
            percentage,
          ),
        );
      },
    );
  }

  Widget _buildCompactIconWithLabel(
    String imagePath,
    String label,
    VoidCallback? onTap,
    BuildContext context,
    int percentage,
  ) {
    final screenSize = MediaQuery.of(context).size;
    final screenHeight = screenSize.height;

    // Set icon size and font size to fixed values
    double iconSize = 32; // Fixed icon size
    double fontSize = 10; // Fixed font size

    double spacingRatio = 0.006;
    double labelHeightRatio = 0.04;

    // Only show the notification dot if not 100% complete
    bool showNotificationDot = percentage < 100;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: screenHeight * spacingRatio),
        Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: iconSize,
                  width: iconSize,
                  alignment: Alignment.center,
                  child: SvgPicture.asset(
                    imagePath,
                    width: iconSize,
                    height: iconSize,
                    fit: BoxFit.contain,
                  ),
                ),
                // Notification dot placed to the top right of the icon
                if (showNotificationDot)
                  Padding(
                    padding: EdgeInsets.only(top: 2, left: 1),
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.005),
        Container(
          height: screenHeight * labelHeightRatio,
          child: OpenSansText(
            label,
            textAlign: TextAlign.center,
            fontSize: fontSize,
            fontWeight: FontWeight.w400,
            color: AppColors.viewColor,
            softWrap: true,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  int _getOptimalColumnCount(double screenWidth) {
    if (screenWidth < 320) return 4; // iPhone SE and very small screens
    if (screenWidth < 360) return 4; // Small phones like older Samsungs
    if (screenWidth < 400) return 4; // Medium phones
    if (screenWidth < 600) return 4; // Large phones like newer iPhones
    if (screenWidth < 900) return 4; // Tablets
    return 4; // Large tablets/desktops
  }

  void _navigateToScreen(String label, context) {
    final appParentDetails = ref.read(appParentDetailsProvider);
    final parentDetails = ref.read(currentParentDetailsProvider);
    final familyDetails = ref.read(familyDetailsStateProvider); 
    final currentWizard = parentDetails?.currentWizardNo ?? 0;
    int currentItem = 0;

    // Extracting values safely
    final parentProfile = appParentDetails?.ParentProfile;
    final parentName = parentProfile?.Name?.trim() ?? "";

    final familyCount = appParentDetails?.FamilyCount ?? 0;
    final kidCount = appParentDetails?.KidCount ?? 0;
    final relationshipStatus = familyDetails.isNotEmpty
    ? familyDetails.first.realtionshipStatusId ?? 0
    : 0;
    final spouseId = familyDetails.isNotEmpty
    ? familyDetails.first.spouseId ?? 0
    : 0;

    // final currentMenu = ref.watch(currentMenuProvider);// FOR TESTING

    if (label == "Application") {
      _navigateTo(ApplicationScreen(), context);
      return;
    }

    // Get the percentage for the selected item
    int percentage = _completionPercentages[label] ?? 100;

    // If incomplete, show bottom sheet first
    if (percentage < 100) {
      // Define steps based on which section was clicked
      List<String> steps = [];
      // int currentStep = 0;

      // if (label == "Application") {
      //   // steps = ["Profile", "Family", "Spouse", "Child"];
      //   // currentStep = 0; // Assuming at Profile step
      // }
      if (label == "My Family") {
        steps = ["Profile", "Family", "Spouse", "Child"];
        if (parentName.isEmpty || currentWizard != 3) {
            currentItem = 0;
          } else {
            currentItem = 1;

            if (familyCount > 0 && relationshipStatus == 2 && spouseId < 0) {
              currentItem = 2;
            } else if (familyCount > 0 && relationshipStatus == 2 && spouseId > 0){
                currentItem = 3;

                if (kidCount > 0) {
                currentItem = 4;
              }
            } else if (familyCount > 0){
                currentItem = 3;
            }
          }
          if(familyCount > 0 && relationshipStatus != 2){
          steps = ["Profile", "Family", "Child"];
          currentItem = 2;

                if (kidCount > 0) {
                currentItem = 4;
              }
          }
        // currentStep = currentItem; // Assuming at Family step
        ref.read(currentStepProvider.notifier).state = currentItem;
      }

      showProgressBottomSheet(
        context,
        title: label,
        steps: steps,
        currentStep: ref.watch(currentStepProvider),
        parentDetails: parentDetails,
        familyCount: familyCount,
        parentName:parentName,
        onContinue: () {
          // Navigate after bottom sheet is dismissed
          // _navigateBasedOnLabel(label, context);
          final stepIndex = ref.read(currentStepProvider);
          switch (steps[stepIndex]) {
      case "Profile":
        if (parentDetails != null) {
          _navigateTo(ProfileSetupScreen(parentDetails: parentDetails), context);
        } else {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Loading profile data...")));
        }

        break;
      case "Family":
        _navigateTo(Myfamily(), context);
        break;
      case "Spouse":
        // Add your Spouse screen here
        break;
      case "Child":
        _navigateTo(ChildDetailsScreen(), context);
        break;
    }
          
        },
      );
    } else {
      // If complete, navigate directly
      _navigateBasedOnLabel(label, context);
    }
  }

  void _navigateBasedOnLabel(String label, BuildContext context) {
    switch (label) {
      case "Application":
        _navigateTo(ApplicationScreen(), context);
        break;
      case "My Family":
        _navigateTo(Myfamily(), context);
        break;
    }
  }

  void _navigateTo(Widget screen, context) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
  }
}
